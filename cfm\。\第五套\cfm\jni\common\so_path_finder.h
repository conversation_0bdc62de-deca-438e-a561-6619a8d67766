#ifndef SO_PATH_FINDER_H
#define SO_PATH_FINDER_H

#include <stddef.h>

// 获取当前进程的包名
// out_pkg_name: 输出包名的缓冲区
// name_size: 缓冲区大小
// 返回值: 成功返回true, 失败返回false
bool get_current_package_name(char* out_pkg_name, size_t name_size);

// 获取特定SO文件的路径
// pkg_name: 包名，例如 "com.tencent.tmgp.cod"
// so_name: SO文件名，例如 "libunite.so"
// out_path: 输出路径的缓冲区
// path_size: 缓冲区大小
// 返回值: 成功返回true, 失败返回false
bool get_app_so_path(const char* pkg_name, const char* so_name, char* out_path, size_t path_size);

// 获取当前进程需要的so库路径
// so_name: SO文件名，例如 "libunite.so"
// out_path: 输出路径的缓冲区
// path_size: 缓冲区大小
// 返回值: 成功返回true, 失败返回false
bool get_current_process_so_path(const char* so_name, char* out_path, size_t path_size);

// 获取应用的lib目录路径
// pkg_name: 包名，例如 "com.tencent.tmgp.cod"
// out_path: 输出路径的缓冲区
// path_size: 缓冲区大小
// 返回值: 成功返回true, 失败返回false
bool get_app_lib_dir(const char* pkg_name, char* out_path, size_t path_size);

// 获取当前进程的lib目录路径
// out_path: 输出路径的缓冲区
// path_size: 缓冲区大小
// 返回值: 成功返回true, 失败返回false
bool get_current_process_lib_dir(char* out_path, size_t path_size);

#endif // SO_PATH_FINDER_H 