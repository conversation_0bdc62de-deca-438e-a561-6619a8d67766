#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
哔哩哔哩批量举报评论工具
作者: Assistant
"""

import http.client
import json
import time
import os
import gzip
from typing import List, Dict, Any

class BilibiliReportTool:
    def __init__(self, config_file: str = "accounts.json"):
        """
        初始化哔哩哔哩举报工具
        
        Args:
            config_file (str): 账号配置文件路径
        """
        self.config_file = config_file
        self.accounts = self.load_accounts()
        
    def load_accounts(self) -> List[Dict[str, str]]:
        """
        从配置文件加载账号信息
        
        Returns:
            List[Dict[str, str]]: 账号列表
        """
        if not os.path.exists(self.config_file):
            print(f"配置文件 {self.config_file} 不存在，请先创建配置文件")
            return []
            
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('accounts', [])
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            return []
    
    def report_comment(self, account: Dict[str, str], oid: str, rpid: str, 
                      reason: int = 4, content: str = "", comment_type: int = 1) -> bool:
        """
        举报评论
        
        Args:
            account (Dict[str, str]): 账号信息
            rpid (str): 评论ID
            reason (int): 举报原因代码，0=其他，4=引战
            content (str): 举报理由（reason=0时必须提供）
            comment_type (int): 评论类型，默认为1
            oid (str): 视频或评论位置ID（可选）
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 创建HTTPS连接
            conn = http.client.HTTPSConnection("api.bilibili.com")
            
            # 构建请求数据（严格按照用户提供的格式）
            # reason 不为 0: access_key, appkey, oid, reason, rpid, type
            # reason 为 0: access_key, appkey, content, oid, reason, rpid, type
            
            if reason == 0:
                # reason=0 的格式
                payload_parts = [
                    f"access_key={account['access_key']}",
                    f"appkey={account['appkey']}",
                    f"content={content}",
                    f"oid={oid}",
                    f"reason={reason}",
                    f"rpid={rpid}",
                    f"type={comment_type}"
                ]
            else:
                # reason不为0 的格式
                payload_parts = [
                    f"access_key={account['access_key']}",
                    f"appkey={account['appkey']}",
                    f"oid={oid}",
                    f"reason={reason}",
                    f"rpid={rpid}",
                    f"type={comment_type}"
                ]
            
            payload = "&".join(payload_parts)
            
            # 更新的请求头
            headers = {
                'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23117RK66C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/136.0.7103.60 Mobile Safari/537.36 os/android model/23117RK66C build/7760700 osVer/15 sdkInt/35 network/2 BiliApp/7760700 mobi_app/android channel/xiaomi Buvid/XU9B8919FE8A69861B8105A55CA82EFB7385A sessionID/64d700be innerVer/7760710 c_locale/zh_CN s_locale/zh_CN disable_rcmd/0 7.76.0 os/android model/23117RK66C mobi_app/android build/7760700 channel/xiaomi_cn_tv.danmaku.bili_20210930 innerVer/7760710 osVer/15 network/2",
                'Connection': "keep-alive",
                'Accept': "application/json, text/plain, */*",
                'Accept-Encoding': "gzip, deflate",
                'Content-Type': "application/x-www-form-urlencoded",
                'native_api_from': "h5",
                'cookie': "Buvid=XU9B8919FE8A69861B8105A55CA82EFB7385A",
                'buvid': "XU9B8919FE8A69861B8105A55CA82EFB7385A",
                'referer': "https://www.bilibili.com/h5/comment/report",
                'content-type': "application/x-www-form-urlencoded; charset=utf-8",
                'env': "prod",
                'app-key': "android64",
                'x-bili-aurora-eid': "U1wJRVkDAVUP",
                'bili-http-engine': "cronet"
            }
            
            # 发送请求（将payload编码为UTF-8字节以支持中文）
            payload_bytes = payload.encode('utf-8')
            conn.request("POST", "/x/v2/reply/report", payload_bytes, headers)
            
            # 获取响应
            res = conn.getresponse()
            data = res.read()
            
            # 处理gzip压缩的响应
            content_encoding = res.getheader('Content-Encoding', '')
            if 'gzip' in content_encoding:
                try:
                    data = gzip.decompress(data)
                except:
                    pass  # 如果解压失败，继续使用原始数据
            
            # 尝试多种编码方式解码
            response_text = ""
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                try:
                    response_text = data.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if not response_text:
                # 如果所有编码都失败，使用错误替换模式
                response_text = data.decode('utf-8', errors='replace')
            
            # 解析响应
            try:
                response_json = json.loads(response_text)
                code = response_json.get('code', -999)
                
                if code == 0:
                    reason_text = self.get_reason_text(reason)
                    # 获取toast内容以确认举报是否真正受理
                    data_obj = response_json.get('data', {})
                    toast_msg = data_obj.get('toast', '')
                    
                    if toast_msg:
                        print(f"账号 {account.get('name', '未知')} 举报成功 (原因: {reason_text}) - {toast_msg}")
                    else:
                        print(f"账号 {account.get('name', '未知')} 举报成功 (原因: {reason_text})")
                    return True
                else:
                    # 根据错误码提供具体的错误信息
                    error_messages = {
                        -101: "账号未登录，access_key可能已过期",
                        -106: "请绑定手机号",
                        -111: "csrf校验失败",
                        -352: "参数错误或请求格式有误",
                        -400: "请求错误，参数可能有误",
                        12008: "已经举报过了",
                        12077: "举报理由过长或过短"
                    }
                    
                    error_msg = error_messages.get(code, response_json.get('message', f'未知错误 (code: {code})'))
                    print(f"账号 {account.get('name', '未知')} 举报失败: {error_msg}")
                    return False
                    
            except json.JSONDecodeError:
                print(f"账号 {account.get('name', '未知')} 响应解析失败: {response_text}")
                return False
                
        except Exception as e:
            print(f"账号 {account.get('name', '未知')} 请求异常: {e}")
            return False
        finally:
            try:
                conn.close()
            except:
                pass
    
    def get_reason_text(self, reason: int) -> str:
        """
        获取举报原因的文本描述
        
        Args:
            reason (int): 举报原因代码
            
        Returns:
            str: 举报原因文本
        """
        reason_map = {
            0: "其他",
            1: "垃圾广告",
            4: "引战",
            8: "视频不相关"
        }
        return reason_map.get(reason, f"未知原因({reason})")
    
    def batch_report(self, rpid: str, reason: int = 4, content: str = "", 
                    comment_type: int = 1, oid: str = "", delay: float = 2.0) -> None:
        """
        批量举报操作
        
        Args:
            rpid (str): 评论ID
            reason (int): 举报原因代码，0=其他，4=引战
            content (str): 举报理由（reason=0时必须提供）
            comment_type (int): 评论类型，默认为1
            oid (str): 视频或评论位置ID（可选）
            delay (float): 请求间隔时间（秒）
        """
        if not self.accounts:
            print("没有可用的账号配置")
            return
        
        # 检查参数
        if reason == 0 and not content.strip():
            print("举报原因选择'其他'时，必须提供举报理由！")
            return
        
        reason_text = self.get_reason_text(reason)
        print(f"开始批量举报操作...")
        print(f"目标评论ID: {rpid}")
        if oid:
            print(f"位置ID: {oid}")
        print(f"举报原因: {reason_text}")
        if content:
            print(f"举报理由: {content}")
        print(f"使用账号数量: {len(self.accounts)}")
        print("-" * 50)
        
        success_count = 0
        for i, account in enumerate(self.accounts, 1):
            print(f"[{i}/{len(self.accounts)}] 正在使用账号: {account.get('name', '未知')}")
            
            if self.report_comment(account, oid, rpid, reason, content, comment_type):
                success_count += 1
            
            # 延时，避免请求过于频繁
            if i < len(self.accounts):
                print(f"等待 {delay} 秒...")
                time.sleep(delay)
        
        print("-" * 50)
        print(f"批量举报完成！成功: {success_count}/{len(self.accounts)}")

def main():
    """主函数"""
    print("=" * 60)
    print("        哔哩哔哩批量举报工具")
    print("=" * 60)
    
    # 初始化工具
    tool = BilibiliReportTool()
    
    if not tool.accounts:
        print("请先配置账号信息！")
        return
    
    try:
        # 获取用户输入
        print(f"\n当前加载了 {len(tool.accounts)} 个账号")
        print("\n请输入以下参数：")
        
        oid = input("请输入 oid (视频或评论位置ID): ").strip()
        if not oid:
            print("oid 不能为空！")
            return
        
        rpid = input("请输入 rpid (评论ID): ").strip()
        if not rpid:
            print("rpid 不能为空！")
            return
        
        print("\n举报原因选项：")
        print("0 - 其他")
        print("1 - 垃圾广告")
        print("4 - 引战")
        print("8 - 视频不相关")
        
        reason_input = input("请输入举报原因 (0/1/4/8) [默认4]: ").strip()
        reason = 4 if not reason_input else int(reason_input)
        
        content = ""
        if reason == 0:
            content = input("请输入举报理由 (选择'其他'时必填): ").strip()
            if not content:
                print("选择'其他'时举报理由不能为空！")
                return
        
        type_input = input("请输入 type (评论类型) [默认1]: ").strip()
        comment_type = 1 if not type_input else int(type_input)
        
        delay_input = input("请输入请求间隔时间(秒) [默认2.0]: ").strip()
        delay = 2.0 if not delay_input else float(delay_input)
        
        # 确认操作
        reason_text = tool.get_reason_text(reason)
        print(f"\n即将使用 {len(tool.accounts)} 个账号举报评论")
        print(f"参数: rpid={rpid}, reason={reason}({reason_text}), type={comment_type}")
        if oid:
            print(f"位置ID: {oid}")
        if content:
            print(f"举报理由: {content}")
        
        print("\n⚠️ 警告：请确保举报理由真实有效，恶意举报可能导致账号被封！")
        confirm = input("确认执行？(y/N): ").strip().lower()
        
        if confirm == 'y':
            tool.batch_report(rpid, reason, content, comment_type, oid, delay)
        else:
            print("操作已取消")
            
    except KeyboardInterrupt:
        print("\n\n操作被用户中断")
    except ValueError as e:
        print(f"输入格式错误: {e}")
    except Exception as e:
        print(f"程序异常: {e}")

if __name__ == "__main__":
    main()
