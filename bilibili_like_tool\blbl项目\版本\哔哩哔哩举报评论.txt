{
  "access_key": "dc6ea404b6ea36af29152628177f5771CjBKe4iNemfzS4vfyh5fHQo7_A1WM8wmsVvm8-mQdb37AoQ95cFiETXHI3OfBDdpsQESVnp3WmNRRzJ0QkJ1SkxvQzRubEpic0J6VGpLQnE0TzhnSGlraFN0Y1F6cEQ4LWJlY3ZscXdDbXNfMEVyRkwtbFlIeDMxR1FaUHFSeGZvQkotUVFLYzF3IIEC",
  "add_blacklist": "false",
  "appkey": "1d8b6e7d45233436",
  "build": "7760700",
  "buvid": "XU9B8919FE8A69861B8105A55CA82EFB7385A",
  "csrf": "2f775716a1d53cca3d63ebbf2b3f32c5",
  "disable_rcmd": "0",
  "gaia_source": "main_h5",
  "mobi_app": "android",
  "oid": "114874369707977",
  "ordering": "time",
  "platform": "android",
  "reason": "4",
  "rpid": "269217321744",
  "scene": "main",
  "scm_action_id": "71122A06",
  "statistics": "{\"appId\":1,\"platform\":3,\"version\":\"7.76.0\",\"abtest\":\"\"}",
  "ts": "1753754365",
  "type": "1",
  "sign": "582d5a85e77c32bd14d50e4f9fe071da"
}

根据这个哔哩哔哩举报评论的请求参数，我来解释每个参数的大致作用：

认证和安全参数
access_key: 用户访问令牌，用于身份验证
appkey: 应用程序密钥，标识客户端应用
csrf: CSRF防护令牌，防止跨站请求伪造攻击
sign: 请求签名，用于验证请求的完整性和真实性
设备和平台信息
buvid: 设备唯一标识符
mobi_app: 移动应用标识，这里是"android"
platform: 平台标识，"android"
build: 应用构建版本号 (7760700)
举报相关核心参数
oid: 被举报内容所属的对象ID (114874369707977，可能是视频AV号)
rpid: 被举报的评论ID (269217321744)
type: 评论类型，1表示视频评论（根据记忆）
reason: 举报原因代码，4表示具体的举报类型
功能配置参数
add_blacklist: 是否添加到黑名单 (false)
disable_rcmd: 是否禁用推荐 (0表示不禁用)
ordering: 排序方式，"time"表示按时间排序
界面和追踪参数
scene: 场景标识，"main"表示主界面
gaia_source: 来源标识，"main_h5"表示来自主页H5页面
scm_action_id: 行为追踪ID，用于数据分析
其他参数
ts: 请求时间戳 (1753754365)
statistics: 统计信息的JSON字符串，包含应用ID、平台、版本等
这个请求的作用是向哔哩哔哩服务器提交一个对特定视频评论的举报，包含了完整的身份验证、设备信息和举报详情。



正常只需要这些就够了
access_key
appkey
oid
reason
rpid
type

保险起见的话可以加一个
add_blacklist





{
  "access_key": "dc6ea404b6ea36af29152628177f5771CjBKe4iNemfzS4vfyh5fHQo7_A1WM8wmsVvm8-mQdb37AoQ95cFiETXHI3OfBDdpsQESVnp3WmNRRzJ0QkJ1SkxvQzRubEpic0J6VGpLQnE0TzhnSGlraFN0Y1F6cEQ4LWJlY3ZscXdDbXNfMEVyRkwtbFlIeDMxR1FaUHFSeGZvQkotUVFLYzF3IIEC",
  "add_blacklist": "false",
  "appkey": "1d8b6e7d45233436",
  "build": "7760700",
  "buvid": "XU9B8919FE8A69861B8105A55CA82EFB7385A",
  "content": "不评",
  "csrf": "2f775716a1d53cca3d63ebbf2b3f32c5",
  "disable_rcmd": "0",
  "gaia_source": "main_h5",
  "mobi_app": "android",
  "oid": "114874369707977",
  "ordering": "time",
  "platform": "android",
  "reason": "0",
  "rpid": "268424582465",
  "scene": "detail",
  "scm_action_id": "E2BECD37",
  "statistics": "{\"appId\":1,\"platform\":3,\"version\":\"7.76.0\",\"abtest\":\"\"}",
  "ts": "1753755346",
  "type": "1",
  "sign": "2dbb2e03abbaa95d0d3858ebba5b0506"
}

这个是带内容的举报
content是举报内容






