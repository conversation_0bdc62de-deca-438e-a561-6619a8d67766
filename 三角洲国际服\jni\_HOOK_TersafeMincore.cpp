#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <android/log.h>
#include "Dobby/dobby.h"
#include "hook_utils.h"
#include "semaphores.h"

#define LOG_TAG "MyHook_TersafeMincore"

// 原始函数指针
static int (*orig_syscall_func)();
static bool syscall_hook_installed = false;

// 替代函数 - 直接返回0，表示执行成功
static int fake_syscall_func() {
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[拦截] 捕获到系统调用 syscall 0xE8");
    return 0;  // 直接返回0
}

int ReverseSyscall() {
    // 等待 cp-cdn 初始化完成
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[等待] 等待 CP_CDN Hook 初始化完成...");
    sem_wait(&parallel_start_sem);
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[初始化] ============= 开始初始化 Syscall Hook =============");
    
    // 循环获取libtersafe.so的基地址
    void* base_addr = nullptr;
    while (!base_addr) {
        long module_base = GetModuleBase("libtersafe.so");
        if (!module_base) {
            __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, 
                "[等待] libtersafe.so还未加载，1秒后重试...");
            sleep(1);
            continue;
        }
        base_addr = (void*)module_base;
    }
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[成功] 获取到libtersafe.so基地址: %p", base_addr);
    
    // 计算目标函数的实际地址
    void *target_addr = (void *)((uintptr_t)base_addr + 0x31B290);
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[信息] 系统调用地址: %p (base: %p + offset: 0x31B290)", target_addr, base_addr);
    
    // Hook函数
    if (DobbyHook(target_addr, (void *)fake_syscall_func, (void **)&orig_syscall_func) == 0) {
        syscall_hook_installed = true;
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
            "[成功] Syscall Hook 安装成功");
    } else {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, 
            "[错误] Syscall Hook 安装失败");
        return -1;
    }
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[初始化] ============= Syscall Hook 初始化完成 =============");
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[完成] Syscall Hook 初始化完成");
    
    return 0;
}

void *init_for_syscall(void *) {
    ReverseSyscall();
    return 0;
}

// 使用定义的宏
void __attribute__ ((constructor(INIT_PRIORITY_TERSAFE_MINCORE))) init_tersafe_mincore() {
    pthread_t t;
    pthread_create(&t, 0, init_for_syscall, 0);
    pthread_detach(t);
}
