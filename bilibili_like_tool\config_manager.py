#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号配置管理工具
用于添加、删除、编辑账号信息
"""

import json
import os
from typing import List, Dict, Any

class ConfigManager:
    def __init__(self, config_file: str = None):
        """
        初始化配置管理器
        
        Args:
            config_file (str): 配置文件路径，如果为None则自动选择最佳位置
        """
        if config_file is None:
            # 优先检查当前目录是否有accounts.json
            current_dir_file = "accounts.json"
            if os.path.exists(current_dir_file):
                self.config_file = current_dir_file
                print(f"发现当前目录的配置文件: {current_dir_file}")
            else:
                # 如果当前目录没有，使用用户文档目录
                user_home = os.path.expanduser("~")
                self.config_file = os.path.join(user_home, "Documents", "bilibili_accounts.json")
                print(f"使用文档目录的配置文件: {self.config_file}")
        else:
            self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        if not os.path.exists(self.config_file):
            return {"accounts": []}
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            return {"accounts": []}
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保目录存在
            config_dir = os.path.dirname(self.config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"配置已保存到: {self.config_file}")
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            print(f"尝试保存的路径: {self.config_file}")
            return False
    
    def add_account(self, name: str, access_key: str, appkey: str) -> bool:
        """
        添加账号
        
        Args:
            name (str): 账号名称
            access_key (str): access_key
            appkey (str): appkey
            
        Returns:
            bool: 添加是否成功
        """
        # 检查账号名是否已存在
        for account in self.config["accounts"]:
            if account["name"] == name:
                print(f"账号名 '{name}' 已存在！")
                return False
        
        # 添加新账号
        new_account = {
            "name": name,
            "access_key": access_key,
            "appkey": appkey
        }
        
        self.config["accounts"].append(new_account)
        
        if self.save_config():
            print(f"账号 '{name}' 添加成功！")
            return True
        else:
            # 回滚
            self.config["accounts"].pop()
            return False
    
    def remove_account(self, name: str) -> bool:
        """
        删除账号
        
        Args:
            name (str): 要删除的账号名称
            
        Returns:
            bool: 删除是否成功
        """
        for i, account in enumerate(self.config["accounts"]):
            if account["name"] == name:
                removed_account = self.config["accounts"].pop(i)
                if self.save_config():
                    print(f"账号 '{name}' 删除成功！")
                    return True
                else:
                    # 回滚
                    self.config["accounts"].insert(i, removed_account)
                    return False
        
        print(f"未找到账号 '{name}'！")
        return False
    
    def list_accounts(self) -> None:
        """显示所有账号"""
        accounts = self.config["accounts"]
        if not accounts:
            print("暂无配置的账号")
            return
        
        print(f"\n当前配置的账号 (共 {len(accounts)} 个):")
        print("-" * 50)
        for i, account in enumerate(accounts, 1):
            print(f"{i}. 账号名: {account['name']}")
            print(f"   access_key: {account['access_key'][:20]}...")
            print(f"   appkey: {account['appkey'][:20]}...")
            print()
    
    def edit_account(self, name: str) -> bool:
        """
        编辑账号信息
        
        Args:
            name (str): 要编辑的账号名称
            
        Returns:
            bool: 编辑是否成功
        """
        for account in self.config["accounts"]:
            if account["name"] == name:
                print(f"正在编辑账号: {name}")
                print(f"当前 access_key: {account['access_key']}")
                print(f"当前 appkey: {account['appkey']}")
                
                new_name = input(f"新账号名 (留空保持 '{name}'): ").strip()
                if new_name and new_name != name:
                    # 检查新名称是否已存在
                    for other_account in self.config["accounts"]:
                        if other_account["name"] == new_name and other_account != account:
                            print(f"账号名 '{new_name}' 已存在！")
                            return False
                    account["name"] = new_name
                
                new_access_key = input("新 access_key (留空保持原值): ").strip()
                if new_access_key:
                    account["access_key"] = new_access_key
                
                new_appkey = input("新 appkey (留空保持原值): ").strip()
                if new_appkey:
                    account["appkey"] = new_appkey
                
                if self.save_config():
                    print("账号信息更新成功！")
                    return True
                else:
                    return False
        
        print(f"未找到账号 '{name}'！")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("     哔哩哔哩账号配置管理工具")
    print("=" * 50)
    
    manager = ConfigManager()
    
    while True:
        print("\n请选择操作:")
        print("1. 添加账号")
        print("2. 删除账号") 
        print("3. 编辑账号")
        print("4. 查看所有账号")
        print("5. 退出")
        
        try:
            choice = input("\n请输入选项 (1-5): ").strip()
            
            if choice == "1":
                print("\n=== 添加账号 ===")
                name = input("账号名称: ").strip()
                if not name:
                    print("账号名称不能为空！")
                    continue
                
                access_key = input("access_key: ").strip()
                if not access_key:
                    print("access_key不能为空！")
                    continue
                
                appkey = input("appkey: ").strip()
                if not appkey:
                    print("appkey不能为空！")
                    continue
                
                manager.add_account(name, access_key, appkey)
            
            elif choice == "2":
                print("\n=== 删除账号 ===")
                manager.list_accounts()
                name = input("请输入要删除的账号名称: ").strip()
                if name:
                    confirm = input(f"确认删除账号 '{name}'? (y/N): ").strip().lower()
                    if confirm == 'y':
                        manager.remove_account(name)
            
            elif choice == "3":
                print("\n=== 编辑账号 ===")
                manager.list_accounts()
                name = input("请输入要编辑的账号名称: ").strip()
                if name:
                    manager.edit_account(name)
            
            elif choice == "4":
                manager.list_accounts()
            
            elif choice == "5":
                print("退出程序")
                break
            
            else:
                print("无效选项，请重新选择！")
        
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"操作失败: {e}")

if __name__ == "__main__":
    main()
