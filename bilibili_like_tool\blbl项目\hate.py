#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
哔哩哔哩批量差评点赞工具
作者: Assistant
"""

import http.client
import json
import time
import os
from typing import List, Dict, Any

class BilibiliHateTool:
    def __init__(self, config_file: str = "accounts.json"):
        """
        初始化哔哩哔哩差评点赞工具
        
        Args:
            config_file (str): 账号配置文件路径
        """
        self.config_file = config_file
        self.accounts = self.load_accounts()
        
    def load_accounts(self) -> List[Dict[str, str]]:
        """
        从配置文件加载账号信息
        
        Returns:
            List[Dict[str, str]]: 账号列表
        """
        if not os.path.exists(self.config_file):
            print(f"配置文件 {self.config_file} 不存在，请先创建配置文件")
            return []
            
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('accounts', [])
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            return []
    
    def hate_comment(self, account: Dict[str, str], oid: str, rpid: str, 
                    action: int = 1, comment_type: int = 1) -> bool:
        """
        对评论进行差评点赞操作
        
        Args:
            account (Dict[str, str]): 账号信息
            oid (str): 视频或评论位置ID
            rpid (str): 评论ID
            action (int): 操作类型，0=取消差评，1=差评点赞
            comment_type (int): 评论类型，默认为1
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 创建HTTPS连接
            conn = http.client.HTTPSConnection("api.bilibili.com")
            
            # 构建请求数据
            payload = (f"access_key={account['access_key']}&"
                      f"action={action}&"
                      f"appkey={account['appkey']}&"
                      f"oid={oid}&"
                      f"rpid={rpid}&"
                      f"type={comment_type}")
            
            # 请求头
            headers = {
                'User-Agent': "Mozilla/5.0 BiliDroid/7.76.0 (<EMAIL>) os/android model/23117RK66C mobi_app/android build/7760700 channel/xiaomi_cn_tv.danmaku.bili_20210930 innerVer/7760710 osVer/15 network/2",
                'Content-Type': "application/x-www-form-urlencoded; charset=utf-8",
                'bili-http-engine': "cronet"
            }
            
            # 发送请求（注意：这里使用hate接口）
            conn.request("POST", "/x/v2/reply/hate", payload, headers)
            
            # 获取响应
            res = conn.getresponse()
            data = res.read()
            response_text = data.decode("utf-8")
            
            # 解析响应
            try:
                response_json = json.loads(response_text)
                code = response_json.get('code', -999)
                
                if code == 0:
                    action_text = "差评点赞" if action == 1 else "取消差评"
                    print(f"账号 {account.get('name', '未知')} {action_text}成功")
                    return True
                else:
                    # 根据错误码提供具体的错误信息
                    error_messages = {
                        -101: "账号未登录，access_key可能已过期",
                        -106: "请绑定手机号",
                        -111: "csrf校验失败",
                        -400: "请求错误，参数可能有误"
                    }
                    
                    error_msg = error_messages.get(code, response_json.get('message', f'未知错误 (code: {code})'))
                    print(f"账号 {account.get('name', '未知')} 操作失败: {error_msg}")
                    return False
                    
            except json.JSONDecodeError:
                print(f"账号 {account.get('name', '未知')} 响应解析失败: {response_text}")
                return False
                
        except Exception as e:
            print(f"账号 {account.get('name', '未知')} 请求异常: {e}")
            return False
        finally:
            try:
                conn.close()
            except:
                pass
    
    def batch_hate(self, oid: str, rpid: str, action: int = 1, 
                  comment_type: int = 1, delay: float = 1.0) -> None:
        """
        批量差评点赞操作
        
        Args:
            oid (str): 视频或评论位置ID
            rpid (str): 评论ID
            action (int): 操作类型，0=取消差评，1=差评点赞
            comment_type (int): 评论类型，默认为1
            delay (float): 请求间隔时间（秒）
        """
        if not self.accounts:
            print("没有可用的账号配置")
            return
        
        action_text = "差评点赞" if action == 1 else "取消差评"
        print(f"开始批量{action_text}操作...")
        print(f"目标: oid={oid}, rpid={rpid}")
        print(f"使用账号数量: {len(self.accounts)}")
        print("-" * 50)
        
        success_count = 0
        for i, account in enumerate(self.accounts, 1):
            print(f"[{i}/{len(self.accounts)}] 正在使用账号: {account.get('name', '未知')}")
            
            if self.hate_comment(account, oid, rpid, action, comment_type):
                success_count += 1
            
            # 延时，避免请求过于频繁
            if i < len(self.accounts):
                print(f"等待 {delay} 秒...")
                time.sleep(delay)
        
        print("-" * 50)
        print(f"批量{action_text}完成！成功: {success_count}/{len(self.accounts)}")

def main():
    """主函数"""
    print("=" * 60)
    print("        哔哩哔哩批量差评点赞工具")
    print("=" * 60)
    
    # 初始化工具
    tool = BilibiliHateTool()
    
    if not tool.accounts:
        print("请先配置账号信息！")
        return
    
    try:
        # 获取用户输入
        print(f"\n当前加载了 {len(tool.accounts)} 个账号")
        print("\n请输入以下参数：")
        
        oid = input("请输入 oid (视频或评论位置ID): ").strip()
        if not oid:
            print("oid 不能为空！")
            return
            
        rpid = input("请输入 rpid (评论ID): ").strip()
        if not rpid:
            print("rpid 不能为空！")
            return
        
        action_input = input("请输入操作类型 (1=差评点赞, 0=取消差评) [默认1]: ").strip()
        action = 1 if not action_input else int(action_input)
        
        type_input = input("请输入 type (评论类型) [默认1]: ").strip()
        comment_type = 1 if not type_input else int(type_input)
        
        delay_input = input("请输入请求间隔时间(秒) [默认1.0]: ").strip()
        delay = 1.0 if not delay_input else float(delay_input)
        
        # 确认操作
        action_text = "差评点赞" if action == 1 else "取消差评"
        print(f"\n即将使用 {len(tool.accounts)} 个账号对评论进行{action_text}操作")
        print(f"参数: oid={oid}, rpid={rpid}, action={action}, type={comment_type}")
        confirm = input("确认执行？(y/N): ").strip().lower()
        
        if confirm == 'y':
            tool.batch_hate(oid, rpid, action, comment_type, delay)
        else:
            print("操作已取消")
            
    except KeyboardInterrupt:
        print("\n\n操作被用户中断")
    except ValueError as e:
        print(f"输入格式错误: {e}")
    except Exception as e:
        print(f"程序异常: {e}")

if __name__ == "__main__":
    main()
