#include "so_path_finder.h"
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <android/log.h>

#define LOG_TAG "MyHook_PathFinder"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

// 获取当前进程的包名
bool get_current_package_name(char* out_pkg_name, size_t name_size) {
    if (!out_pkg_name || name_size <= 0) {
        LOGE("参数无效");
        return false;
    }
    
    // 打开/proc/self/cmdline读取进程名
    FILE* fp = fopen("/proc/self/cmdline", "r");
    if (!fp) {
        LOGE("无法打开/proc/self/cmdline");
        return false;
    }
    
    // 读取内容
    size_t read_size = fread(out_pkg_name, 1, name_size - 1, fp);
    fclose(fp);
    
    if (read_size <= 0) {
        LOGE("读取/proc/self/cmdline失败");
        return false;
    }
    
    // 确保字符串以空字符结尾
    out_pkg_name[read_size] = '\0';
    
    // cmdline可能包含多个以\0分隔的字符串，我们只需要第一个
    for (size_t i = 0; i < read_size; i++) {
        if (out_pkg_name[i] == '\0') {
            out_pkg_name[i] = '\0';
            break;
        }
    }
    
    LOGI("当前进程包名: %s", out_pkg_name);
    return true;
}

// 获取应用的lib目录路径
bool get_app_lib_dir(const char* pkg_name, char* out_path, size_t path_size) {
    if (!pkg_name || !out_path || path_size <= 0) {
        LOGE("参数无效");
        return false;
    }
    
    // 构建pm命令
    char cmd[256];
    snprintf(cmd, sizeof(cmd), "pm path %s | sed 's/package://; s/base\\.apk//'", pkg_name);
    
    // 执行pm命令获取应用路径
    FILE* pipe = popen(cmd, "r");
    if (!pipe) {
        LOGE("无法执行pm命令");
        return false;
    }
    
    char buffer[512];
    if (fgets(buffer, sizeof(buffer), pipe) != NULL) {
        // 移除末尾可能的换行符
        char* newline = strchr(buffer, '\n');
        if (newline) *newline = '\0';
        
        // 构建lib路径
        snprintf(out_path, path_size, "%slib/arm64/", buffer);
        LOGI("通过pm命令获取的lib路径: %s", out_path);
        
        pclose(pipe);
        
        // 检查目录是否存在
        if (access(out_path, F_OK) == 0) {
            LOGI("找到lib目录: %s", out_path);
            return true;
        } else {
            LOGE("通过pm找到的路径无效: %s", out_path);
        }
    } else {
        pclose(pipe);
        LOGE("pm命令未返回有效路径");
    }
    
    return false;
}

// 获取当前进程的lib目录路径
bool get_current_process_lib_dir(char* out_path, size_t path_size) {
    if (!out_path || path_size <= 0) {
        LOGE("参数无效");
        return false;
    }
    
    // 获取当前进程包名
    char pkg_name[128] = {0};
    if (!get_current_package_name(pkg_name, sizeof(pkg_name))) {
        LOGE("无法获取当前进程包名");
        return false;
    }
    
    // 使用包名获取lib目录路径
    return get_app_lib_dir(pkg_name, out_path, path_size);
}

// 使用shell命令获取应用的so库路径
bool get_app_so_path(const char* pkg_name, const char* so_name, char* out_path, size_t path_size) {
    if (!pkg_name || !so_name || !out_path || path_size <= 0) {
        LOGE("参数无效");
        return false;
    }
    
    // 获取lib目录路径
    char lib_dir[512] = {0};
    if (!get_app_lib_dir(pkg_name, lib_dir, sizeof(lib_dir))) {
        return false;
    }
    
    // 拼接完整SO路径
    snprintf(out_path, path_size, "%s%s", lib_dir, so_name);
    LOGI("完整SO路径: %s", out_path);
    
    // 检查文件是否存在
    if (access(out_path, F_OK) == 0) {
        LOGI("找到SO文件: %s", out_path);
        return true;
    }
    
    LOGE("SO文件不存在: %s", out_path);
    return false;
}

// 获取当前进程需要的so库路径
bool get_current_process_so_path(const char* so_name, char* out_path, size_t path_size) {
    if (!so_name || !out_path || path_size <= 0) {
        LOGE("参数无效");
        return false;
    }
    
    // 获取当前进程包名
    char pkg_name[128] = {0};
    if (!get_current_package_name(pkg_name, sizeof(pkg_name))) {
        LOGE("无法获取当前进程包名");
        return false;
    }
    
    // 使用包名获取SO路径
    return get_app_so_path(pkg_name, so_name, out_path, path_size);
} 