LOCAL_PATH := $(call my-dir)
MAIN_LOCAL_PATH := $(call my-dir)


include $(CLEAR_VARS)
LOCAL_MODULE := libdobby
LOCAL_SRC_FILES := Dobby/libdobby.a
include $(PREBUILT_STATIC_LIBRARY)


include $(CLEAR_VARS)
LOCAL_MODULE    := UE5
LOCAL_CPPFLAGS := -g0 -O2 -w -fvisibility=hidden -fno-stack-protector -fms-extensions -s -DDBG=0 -DNDEBUG=1 -std=c++17
LOCAL_LDLIBS += -lc -lz -lm -llog -landroid -lEGL -lGLESv2 -lGLESv3 -lGLESv1_CM


LOCAL_SRC_FILES := _HOOK_RESTORE.cpp common/signal_controller.cpp common/so_path_finder.cpp
LOCAL_STATIC_LIBRARIES := libdobby

include $(BUILD_SHARED_LIBRARY)