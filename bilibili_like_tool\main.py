#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
哔哩哔哩批量点赞工具
作者: Assistant
"""

import http.client
import json
import time
import os
from typing import List, Dict, Any

class BilibiliLikeTool:
    def __init__(self, config_file: str = "accounts.json"):
        """
        初始化哔哩哔哩点赞工具
        
        Args:
            config_file (str): 账号配置文件路径
        """
        self.config_file = config_file
        self.accounts = self.load_accounts()
        
    def load_accounts(self) -> List[Dict[str, str]]:
        """
        从配置文件加载账号信息
        
        Returns:
            List[Dict[str, str]]: 账号列表
        """
        if not os.path.exists(self.config_file):
            print(f"配置文件 {self.config_file} 不存在，请先创建配置文件")
            return []
            
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('accounts', [])
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            return []
    
    def like_comment(self, account: Dict[str, str], oid: str, rpid: str, 
                    action: int = 1, comment_type: int = 1) -> bool:
        """
        对评论进行点赞操作
        
        Args:
            account (Dict[str, str]): 账号信息
            oid (str): 视频或评论位置ID
            rpid (str): 评论ID
            action (int): 操作类型，0=取消点赞，1=点赞
            comment_type (int): 评论类型，默认为1
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 创建HTTPS连接
            conn = http.client.HTTPSConnection("api.bilibili.com")
            
            # 构建请求数据
            payload = (f"access_key={account['access_key']}&"
                      f"action={action}&"
                      f"appkey={account['appkey']}&"
                      f"oid={oid}&"
                      f"rpid={rpid}&"
                      f"type={comment_type}")
            
            # 请求头
            headers = {
                'User-Agent': "Mozilla/5.0 BiliDroid/7.76.0 (<EMAIL>) os/android model/23117RK66C mobi_app/android build/7760700 channel/xiaomi_cn_tv.danmaku.bili_20210930 innerVer/7760710 osVer/15 network/2",
                'Content-Type': "application/x-www-form-urlencoded; charset=utf-8",
                'bili-http-engine': "cronet"
            }
            
            # 发送请求
            conn.request("POST", "/x/v2/reply/action", payload, headers)
            
            # 获取响应
            res = conn.getresponse()
            data = res.read()
            response_text = data.decode("utf-8")
            
            # 解析响应
            try:
                response_json = json.loads(response_text)
                code = response_json.get('code', -999)
                
                if code == 0:
                    action_text = "点赞" if action == 1 else "取消点赞"
                    print(f"账号 {account.get('name', '未知')} {action_text}成功")
                    return True
                else:
                    # 根据错误码提供具体的错误信息
                    error_messages = {
                        -101: "账号未登录，access_key可能已过期",
                        -106: "请绑定手机号",
                        -111: "csrf校验失败",
                        -400: "请求错误，参数可能有误"
                    }
                    
                    error_msg = error_messages.get(code, response_json.get('message', f'未知错误 (code: {code})'))
                    print(f"账号 {account.get('name', '未知')} 操作失败: {error_msg}")
                    return False
                    
            except json.JSONDecodeError:
                print(f"账号 {account.get('name', '未知')} 响应解析失败: {response_text}")
                return False
                
        except Exception as e:
            print(f"账号 {account.get('name', '未知')} 请求异常: {e}")
            return False
        finally:
            try:
                conn.close()
            except:
                pass
    
    def parse_account_range(self, range_str: str) -> tuple:
        """
        解析账号范围字符串
        
        Args:
            range_str (str): 范围字符串，格式如 "26-35" 或空字符串
            
        Returns:
            tuple: (start_index, end_index) 都是0基索引，如果是全部则返回 (0, len(accounts))
        """
        if not range_str.strip():
            return 0, len(self.accounts)
        
        try:
            if '-' in range_str:
                start, end = range_str.split('-', 1)
                start_pos = int(start.strip())
                end_pos = int(end.strip())
                
                # 用户输入的是1基索引，转换为0基索引
                start_index = max(0, start_pos - 1)
                end_index = min(len(self.accounts), end_pos)
                
                if start_index >= end_index:
                    print(f"警告: 范围无效 {start_pos}-{end_pos}，将使用全部账号")
                    return 0, len(self.accounts)
                
                return start_index, end_index
            else:
                # 单个数字，视为只使用这一个账号
                pos = int(range_str.strip())
                start_index = max(0, pos - 1)
                end_index = min(len(self.accounts), pos)
                return start_index, end_index
                
        except ValueError:
            print(f"警告: 范围格式错误 '{range_str}'，将使用全部账号")
            return 0, len(self.accounts)

    def batch_like(self, oid: str, rpid: str, action: int = 1, 
                  comment_type: int = 1, delay: float = 1.0, account_range: str = "") -> None:
        """
        批量点赞操作
        
        Args:
            oid (str): 视频或评论位置ID
            rpid (str): 评论ID
            action (int): 操作类型，0=取消点赞，1=点赞
            comment_type (int): 评论类型，默认为1
            delay (float): 请求间隔时间（秒）
            account_range (str): 账号范围，格式如 "26-35"，空字符串表示全部
        """
        if not self.accounts:
            print("没有可用的账号配置")
            return
        
        # 解析账号范围
        start_index, end_index = self.parse_account_range(account_range)
        selected_accounts = self.accounts[start_index:end_index]
        
        if not selected_accounts:
            print("没有符合范围的账号")
            return
        
        action_text = "点赞" if action == 1 else "取消点赞"
        print(f"开始批量{action_text}操作...")
        print(f"目标: oid={oid}, rpid={rpid}")
        
        if account_range.strip():
            print(f"使用账号范围: {account_range} (第{start_index+1}-{end_index}个账号)")
        else:
            print("使用全部账号")
        
        print(f"使用账号数量: {len(selected_accounts)}")
        print("-" * 50)
        
        success_count = 0
        for i, account in enumerate(selected_accounts, 1):
            actual_position = start_index + i  # 显示实际的账号位置（1基索引）
            print(f"[{i}/{len(selected_accounts)}] (第{actual_position}个账号) 正在使用账号: {account.get('name', '未知')}")
            
            if self.like_comment(account, oid, rpid, action, comment_type):
                success_count += 1
            
            # 延时，避免请求过于频繁
            if i < len(selected_accounts):
                print(f"等待 {delay} 秒...")
                time.sleep(delay)
        
        print("-" * 50)
        print(f"批量{action_text}完成！成功: {success_count}/{len(selected_accounts)}")

def main():
    """主函数"""
    print("=" * 60)
    print("        哔哩哔哩批量点赞工具")
    print("=" * 60)
    
    # 初始化工具
    tool = BilibiliLikeTool()
    
    if not tool.accounts:
        print("请先配置账号信息！")
        return
    
    try:
        # 获取用户输入
        print(f"\n当前加载了 {len(tool.accounts)} 个账号")
        print("\n请输入以下参数：")
        
        oid = input("请输入 oid (视频或评论位置ID): ").strip()
        if not oid:
            print("oid 不能为空！")
            return
            
        rpid = input("请输入 rpid (评论ID): ").strip()
        if not rpid:
            print("rpid 不能为空！")
            return
        
        action_input = input("请输入操作类型 (1=点赞, 0=取消点赞) [默认1]: ").strip()
        action = 1 if not action_input else int(action_input)
        
        type_input = input("请输入 type (评论类型) [默认1]: ").strip()
        comment_type = 1 if not type_input else int(type_input)
        
        delay_input = input("请输入请求间隔时间(秒) [默认1.0]: ").strip()
        delay = 1.0 if not delay_input else float(delay_input)
        
        # 获取账号范围
        print(f"\n当前总共有 {len(tool.accounts)} 个账号")
        range_input = input("请输入要使用的账号范围 (格式: 26-35，单个数字如 10，或直接回车使用全部): ").strip()
        
        # 验证范围并显示信息
        if range_input:
            start_index, end_index = tool.parse_account_range(range_input)
            selected_count = end_index - start_index
            print(f"将使用第 {start_index+1} 到第 {end_index} 个账号，共 {selected_count} 个账号")
        else:
            selected_count = len(tool.accounts)
            print(f"将使用全部 {selected_count} 个账号")
        
        # 确认操作
        action_text = "点赞" if action == 1 else "取消点赞"
        print(f"\n即将使用 {selected_count} 个账号对评论进行{action_text}操作")
        print(f"参数: oid={oid}, rpid={rpid}, action={action}, type={comment_type}")
        if range_input:
            print(f"账号范围: {range_input}")
        confirm = input("确认执行？(y/N): ").strip().lower()
        
        if confirm == 'y':
            tool.batch_like(oid, rpid, action, comment_type, delay, range_input)
        else:
            print("操作已取消")
            
    except KeyboardInterrupt:
        print("\n\n操作被用户中断")
    except ValueError as e:
        print(f"输入格式错误: {e}")
    except Exception as e:
        print(f"程序异常: {e}")

if __name__ == "__main__":
    main()
