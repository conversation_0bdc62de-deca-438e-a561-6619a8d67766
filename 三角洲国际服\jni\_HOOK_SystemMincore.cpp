#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <android/log.h>
#include "Dobby/dobby.h"
#include "hook_utils.h"
#include "semaphores.h"

#define LOG_TAG "MyHook_SystemMincore"

// 原始mincore函数指针
static int (*orig_mincore)(const void *addr, size_t length, unsigned char *vec);
static bool hook_installed = false;

// hook后的mincore函数
static int fake_mincore(const void *addr, size_t length, unsigned char *vec) {
    if (!hook_installed) return orig_mincore(addr, length, vec);
    
    //__android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
    //    "[拦截] 捕获到mincore调用: addr=%p, length=%zu", addr, length);
    return 0;
}

int ReverseCheatingMincore() {
    // 等待 cp-cdn 初始化完成
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[等待] 等待 CP_CDN Hook 初始化完成...");
    sem_wait(&parallel_start_sem);
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[初始化] ============= 开始初始化 Mincore Hook =============");
        
    // 获取mincore函数地址
    void *mincore_addr = DobbySymbolResolver(NULL, "mincore");
    if (!mincore_addr) {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, 
            "[错误] 未找到mincore函数");
        return -1;
    }
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[地址] 找到mincore函数地址: %p", mincore_addr);
        
    // Hook mincore函数
    if (DobbyHook(mincore_addr, (void *)fake_mincore, (void **)&orig_mincore) == 0) {
        hook_installed = true;
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
            "[成功] Mincore Hook 安装成功");
    } else {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, 
            "[错误] Mincore Hook 安装失败");
        return -1;
    }
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[完成] Mincore Hook 初始化完成");
    
    return 0;
}

void *init_for_system_mincore(void *) {
    ReverseCheatingMincore();
    return 0;
}

// 使用定义的宏
void __attribute__ ((constructor(INIT_PRIORITY_SYSTEM_MINCORE))) init_system_mincore()
{
    pthread_t t;
    pthread_create(&t, 0, init_for_system_mincore, 0);
    pthread_detach(t);
}
