#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <android/log.h>
#include <sys/mman.h>
#include <dlfcn.h>
#include <link.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <vector>
#include "common/so_path_finder.h"

// 日志标签定义
#define LOG_TAG "MyHook_MemoryMap"

// 定义替换库和原始库的映射关系
static const struct {
    const char* replaced_name;  // 替换后的库名
    const char* original_name;  // 原始库名
    int delay_seconds;          // 执行前等待的秒数
    int min_segments;           // 最少需要的内存段数量 (设置为 -1 表示不启用)
    int target_segments;        // 目标内存段数量 (设置为 -1 表示不启用)
    int process_delay_seconds;  // 处理内存段前等待的秒数
} nmc_to_so_map[] = {
    {"libunity.so", "libuni8e.so", 2, -1, 2, 0},
    {"libil2cpp.so", "libil7cpp.so", 0, -1, 29, 0},
    {"libil3cpp.so", "libil2cpp.so", 0, -1, 5, 0}
};
static const int map_count = sizeof(nmc_to_so_map) / sizeof(nmc_to_so_map[0]);  // 自动计算映射数量

// 存储原始SO文件的路径
static char original_so_paths[map_count][512] = {{0}};
// 库目录路径
static char lib_dir_path[512] = {0};

// 内存段信息结构体
struct NmcSegmentInfo {
    unsigned long start;        // 起始地址
    unsigned long end;          // 结束地址
    size_t size;                // 大小
    int segment_index;          // 段索引
    char name[64];              // 名称
    char perms[5];              // 权限
};

// 将权限字符串转换为mprotect所需的保护标志
static int perms_to_prot(const char* perms) {
    int prot = 0;
    if (perms[0] == 'r') prot |= PROT_READ;
    if (perms[1] == 'w') prot |= PROT_WRITE;
    if (perms[2] == 'x') prot |= PROT_EXEC;
    return prot;
}

// 重映射内存段
// 将替换库在内存中的段重新映射为原始库
int RemapMemorySegments() {
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[开始] ============= 开始内存段重映射操作 =============");
    
    // 获取库目录路径
    if (!get_current_process_lib_dir(lib_dir_path, sizeof(lib_dir_path))) {
        __android_log_print(ANDROID_LOG_WARN, LOG_TAG, 
            "[警告] 无法获取库目录路径，部分功能可能无法正常工作");
        
        // 如果获取失败，显示当前包名
        char pkg_name[128];
        if (get_current_package_name(pkg_name, sizeof(pkg_name))) {
            __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                "[信息] 当前包名: %s", pkg_name);
        }
    } else {
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
            "[信息] 库目录路径: %s", lib_dir_path);
    }
    
    // 构建原始SO文件的完整路径
    for (int i = 0; i < map_count; i++) {
        // 拼接SO文件路径（无论文件是否存在）
        snprintf(original_so_paths[i], sizeof(original_so_paths[i]), "%s%s", 
                lib_dir_path, nmc_to_so_map[i].original_name);
        
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
            "[配置] 设置 %s 对应的原始库路径: %s", 
            nmc_to_so_map[i].replaced_name, original_so_paths[i]);
    }
    
    // 顺序处理每种映射关系
    for (int map_idx = 0; map_idx < map_count; map_idx++) {
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
            "[处理] 开始处理映射关系: %s -> %s (延时: %d秒)", 
            nmc_to_so_map[map_idx].replaced_name, nmc_to_so_map[map_idx].original_name,
            nmc_to_so_map[map_idx].delay_seconds);
        
        // 如果设置了延时，则先等待
        if (nmc_to_so_map[map_idx].delay_seconds > 0) {
            __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                "[延时] 等待 %d 秒后执行映射...", nmc_to_so_map[map_idx].delay_seconds);
            sleep(nmc_to_so_map[map_idx].delay_seconds);
        }
        
        std::vector<NmcSegmentInfo> current_segments;  // 仅存储当前处理的映射关系的内存段
        bool found_segments = false;
        int retry_count = 0;
        
        // 无限循环查找，直到找到目标库的内存段
        while (!found_segments) {
            // 打开当前进程的内存映射信息文件
            FILE *maps_fp = fopen("/proc/self/maps", "r");
            if (!maps_fp) {
                __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, 
                    "[错误] 无法打开/proc/self/maps文件");
                sleep(0.1);  // 等待0.1秒后重试
                continue;
            }
            
            char line[1024];
            current_segments.clear();  // 清空上次可能找到的段信息
            
            // 解析内存映射文件，查找当前替换库的内存段
            while (fgets(line, sizeof(line), maps_fp)) {
                if (strstr(line, nmc_to_so_map[map_idx].replaced_name)) {
                    unsigned long start, end;
                    char perms[5];
                    if (sscanf(line, "%lx-%lx %4s", &start, &end, perms) == 3) {
                        NmcSegmentInfo segment;
                        
                        segment.start = start;
                        segment.end = end;
                        segment.size = end - start;
                        segment.segment_index = map_idx;
                        strncpy(segment.name, nmc_to_so_map[map_idx].replaced_name, sizeof(segment.name));
                        strncpy(segment.perms, perms, sizeof(segment.perms));
                        
                        current_segments.push_back(segment);
                        
                        // __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                        //     "[扫描] 发现目标库段: 0x%lx-0x%lx 权限:%s 名称:%s", 
                        //     start, end, perms, nmc_to_so_map[map_idx].replaced_name);
                    }
                }
            }
            
            fclose(maps_fp);





            // 显示当前映射关系将要重映射的内存段数量
            bool segments_match_criteria = false;
            
            // 如果min_segments和target_segments都为-1，则不检查段数（直接通过）
            if (nmc_to_so_map[map_idx].min_segments == -1 && nmc_to_so_map[map_idx].target_segments == -1) {
                segments_match_criteria = !current_segments.empty();
                if (segments_match_criteria) {
                    // __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                    //     "[扫描结果] 跳过段数检查，发现 %s 的 %zu 个内存段", 
                    //     nmc_to_so_map[map_idx].replaced_name, current_segments.size());
                }
            }
            // 如果min_segments有效，优先检查最小段数
            else if (nmc_to_so_map[map_idx].min_segments > 0) {
                segments_match_criteria = !current_segments.empty() && 
                                         current_segments.size() >= (size_t)nmc_to_so_map[map_idx].min_segments;
                if (segments_match_criteria) {
                    // __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                    //     "[扫描结果] 共找到 %s 的 %zu 个内存段，满足最小段数要求 %d", 
                    //     nmc_to_so_map[map_idx].replaced_name, current_segments.size(),
                    //     nmc_to_so_map[map_idx].min_segments);
                }
            }
            // 如果min_segments无效但target_segments有效，检查目标段数
            else if (nmc_to_so_map[map_idx].target_segments > 0) {
                segments_match_criteria = !current_segments.empty() && 
                                         current_segments.size() == (size_t)nmc_to_so_map[map_idx].target_segments;
                if (segments_match_criteria) {
                    // __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                    //     "[扫描结果] 共找到 %s 的 %zu 个内存段，精确匹配目标段数 %d", 
                    //     nmc_to_so_map[map_idx].replaced_name, current_segments.size(),
                    //     nmc_to_so_map[map_idx].target_segments);
                }
            }
            // 都无效时，只要有段就通过（保险措施）
            else {
                segments_match_criteria = !current_segments.empty();
                if (segments_match_criteria) {
                    // __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                    //     "[扫描结果] 无段数限制，发现 %s 的 %zu 个内存段", 
                    //     nmc_to_so_map[map_idx].replaced_name, current_segments.size());
                }
            }
            
            if (segments_match_criteria) {
                found_segments = true;  // 找到了符合条件的目标库内存段，跳出循环
            } else {
                // 未找到符合条件的目标库内存段，等待一段时间后重试
                retry_count++;
                // __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                //     "[等待] 未找到足够数量的 %s 内存段 (当前: %zu, 需要: %d)，等待后重试 (第 %d 次)", 
                //     nmc_to_so_map[map_idx].replaced_name, current_segments.size(), 
                //     nmc_to_so_map[map_idx].min_segments, retry_count);
                
                sleep(0.1);  // 等待0.1秒
            }
        }

        // 如果设置了处理前延时，则先等待
        if (nmc_to_so_map[map_idx].process_delay_seconds > 0) {
            __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                "[延时] 处理内存段前等待 %d 秒...", nmc_to_so_map[map_idx].process_delay_seconds);
            sleep(nmc_to_so_map[map_idx].process_delay_seconds);
        }








        // 处理当前映射关系的所有内存段
        for (size_t i = 0; i < current_segments.size(); i++) {
            const char* so_path = original_so_paths[map_idx];
            
            __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                "[处理] 内存段 %zu/%zu: 0x%lx-0x%lx (%s)", 
                i+1, current_segments.size(), current_segments[i].start, current_segments[i].end,
                current_segments[i].name);
            
            size_t size = current_segments[i].size;
            
            const char* cur_perms = current_segments[i].perms;
            int cur_prot = perms_to_prot(cur_perms);
            
            __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                "[属性] 目标段权限: %s (将在复制后应用)", cur_perms);
            // 先检查原始SO文件是否存在
            struct stat file_stat;
            void* new_addr = NULL;
            bool using_file_mapping = false;
            
            // 检查文件是否存在且可访问
            if (access(so_path, F_OK) == 0) {
                __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                    "[检查] 原始SO文件存在: %s，尝试文件映射", so_path);
                
                // 尝试打开原始SO文件
                int fd = open(so_path, O_RDONLY);
                if (fd != -1) {
                    if (fstat(fd, &file_stat) == 0) {
                        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                            "[映射] 使用文件映射方式，初始使用RWX权限");
                        
                        // 创建可读写执行的内存映射
                        new_addr = mmap(NULL, size, 
                            PROT_READ | PROT_WRITE | PROT_EXEC,
                            MAP_PRIVATE, fd, 0);
                        
                        if (new_addr != MAP_FAILED) {
                            using_file_mapping = true;
                        } else {
                            __android_log_print(ANDROID_LOG_WARN, LOG_TAG, 
                                "[警告] 文件映射失败: %s，将使用匿名映射", strerror(errno));
                        }
                    }
                    close(fd);
                } else {
                    __android_log_print(ANDROID_LOG_WARN, LOG_TAG, 
                        "[警告] 文件存在但无法打开: %s，将使用匿名映射", strerror(errno));
                }
            } else {
                __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                    "[检查] 原始SO文件不存在: %s，将使用匿名映射", so_path);
            }
            
            // 如果文件不存在或映射失败，使用匿名映射
            if (!using_file_mapping) {
                __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                    "[映射] 使用匿名映射方式，初始使用RWX权限");
                
                new_addr = mmap(NULL, size, 
                    PROT_READ | PROT_WRITE | PROT_EXEC,
                    MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);
                
                if (new_addr != MAP_FAILED) {
                    // 不再设置匿名映射的名称
                    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                        "[映射] 匿名映射成功完成");
                }
            }
            
            // 检查内存映射是否成功
            if (new_addr == MAP_FAILED) {
                __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, 
                    "[错误] 创建内存映射失败: %s", strerror(errno));
                continue;
            }
            
            // 复制原始内存内容到新映射
            memcpy(new_addr, (void*)current_segments[i].start, size);
            
            // 调整内存段权限为原始权限
            if (mprotect(new_addr, size, cur_prot) == 0) {
                __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                    "[权限] 已设置新映射权限: %s", cur_perms);
            } else {
                __android_log_print(ANDROID_LOG_WARN, LOG_TAG, 
                    "[警告] 权限设置失败: %s，保持RWX权限", strerror(errno));
            }
            
            // 重新映射到原始内存位置，实现替换
            if (mremap(new_addr, size, size, 
                MREMAP_MAYMOVE | MREMAP_FIXED, (void*)current_segments[i].start) == MAP_FAILED) {
                __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, 
                    "[错误] 重映射失败: %s", strerror(errno));
                munmap(new_addr, size);
                continue;
            }
            
            __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
                "[成功] 内存段已重映射: 0x%lx-0x%lx 名称:%s 权限:%s", 
                current_segments[i].start, current_segments[i].end, 
                so_path, cur_perms);
        }
        
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
            "[完成] 映射关系 %s -> %s 处理完毕，总共重映射了 %zu 个内存段", 
            nmc_to_so_map[map_idx].replaced_name, nmc_to_so_map[map_idx].original_name,
            current_segments.size());
    }
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[完成] 所有映射关系处理完毕");
    
    return 0;
}

// 处理线程函数
// 直接执行内存重映射操作
void *process_memory_thread(void *) {
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[初始化] 开始执行内存重映射操作");
    
    // 执行内存重映射
    int result = RemapMemorySegments();
    
    if (result == 0) {
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
            "[状态] 内存重映射操作已成功完成");
    } else {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, 
            "[错误] 内存重映射操作失败");
    }
    
    return 0;
}

// 构造函数，在库加载时自动执行
// 创建并启动处理线程
void __attribute__ ((constructor(200))) init_memory_remapper() {
    pthread_t t;
    pthread_create(&t, 0, process_memory_thread, 0);
    pthread_detach(t);
}
