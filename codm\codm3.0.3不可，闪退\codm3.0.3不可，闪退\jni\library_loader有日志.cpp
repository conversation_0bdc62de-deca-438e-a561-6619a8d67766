#include <jni.h>
#include <android/log.h>
#include <dlfcn.h>
#include <unistd.h>
#include <string.h>
#include <pthread.h>
#include <stdlib.h>
#include "so_path_finder.h"

#define LOG_TAG "MyHook_LibraryLoader"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// SO加载信息，包含名称和延迟秒数
struct SoInfo { const char* name; int delay; };

// 需要自动加载的SO名称和延迟配置数组
static const SoInfo g_so_infos[] = {{"libunity.so", 0}, {"libUE5.so", 1}}; // 延迟5秒

// 保存每个SO的句柄
static void* g_so_handles[sizeof(g_so_infos)/sizeof(g_so_infos[0])] = {NULL};

// 前置声明 load_target_so，避免未声明错误
bool load_target_so(const char* so_name, void** handle_ptr);

// 线程参数定义
struct ThreadArg { const char* name; void** handle; int delay; };

// 后台加载线程入口
static void* load_thread(void* arg) {
    ThreadArg* ta = (ThreadArg*)arg;
    if (ta->delay > 0) sleep(ta->delay);
    load_target_so(ta->name, ta->handle);
    free(ta);
    return NULL;
}

// 通用SO加载函数，按名称加载SO并保存句柄
bool load_target_so(const char* so_name, void** handle_ptr) {
    char so_path[512] = {0};
    // 使用so_path_finder获取SO路径
    if (!get_current_process_so_path(so_name, so_path, sizeof(so_path))) {
        LOGE("无法获取%s路径", so_name);
        return false;
    }
    // 检查文件是否存在
    if (access(so_path, F_OK) != 0) {
        LOGE("文件不存在: %s", so_path);
        return false;
    }
    // 尝试使用dlopen打开
    *handle_ptr = dlopen(so_path, RTLD_NOW);
    if (*handle_ptr) {
        // LOGI("成功加载%s", so_name);
        return true;
    } else {
        LOGE("加载%s失败: %s", so_name, dlerror());
        return false;
    }
}

// 初始化库的函数，添加构造函数属性使其在库加载时自动执行
void __attribute__ ((constructor(100))) init_library() {
    // 启动后台线程加载SO，避免阻塞主线程
    for (size_t i = 0; i < sizeof(g_so_infos)/sizeof(g_so_infos[0]); ++i) {
        ThreadArg* ta = (ThreadArg*)malloc(sizeof(ThreadArg));
        ta->name = g_so_infos[i].name;
        ta->handle = &g_so_handles[i];
        ta->delay = g_so_infos[i].delay;
        pthread_t tid;
        pthread_create(&tid, NULL, load_thread, ta);
        pthread_detach(tid);
    }
}

/* 
// 保留JNI_OnLoad函数，以便通过Java方式加载时也能正常使用
extern "C" JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
    JNIEnv* env;
    if (vm->GetEnv(reinterpret_cast<void**>(&env), JNI_VERSION_1_6) != JNI_OK) {
        return JNI_ERR;
    }
    
    LOGI("JNI_OnLoad被调用，但主要初始化已通过构造函数完成");
    
    return JNI_VERSION_1_6;
}
*/ 