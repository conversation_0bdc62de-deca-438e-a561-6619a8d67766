#include "signal_controller.h"

// 声明全局互斥锁和条件变量
pthread_mutex_t g_hook_mutex = PTHREAD_MUTEX_INITIALIZER;
pthread_cond_t g_hook_cond = PTHREAD_COND_INITIALIZER;
volatile int g_unified_completed = 0;

// 初始化信号控制器
void init_signal_controller() {
    // 互斥锁和条件变量已通过静态初始化器初始化
    g_unified_completed = 0;
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG_SIGNAL, "信号控制器已初始化");
}

// 发送信号表示_HOOK_UNIFIED已完成
void signal_unified_completed() {
    pthread_mutex_lock(&g_hook_mutex);
    g_unified_completed = 1;
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG_SIGNAL, "发送信号：_HOOK_UNIFIED已完成");
    pthread_cond_broadcast(&g_hook_cond);
    pthread_mutex_unlock(&g_hook_mutex);
}

// 等待_HOOK_UNIFIED完成的信号
void wait_for_unified_completion() {
    pthread_mutex_lock(&g_hook_mutex);
    while (g_unified_completed == 0) {
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG_SIGNAL, "等待_HOOK_UNIFIED完成...");
        pthread_cond_wait(&g_hook_cond, &g_hook_mutex);
    }
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG_SIGNAL, "_HOOK_UNIFIED已完成，开始执行恢复操作");
    pthread_mutex_unlock(&g_hook_mutex);
} 