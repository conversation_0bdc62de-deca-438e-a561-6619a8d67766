#ifndef HOOK_UTILS_H
#define HOOK_UTILS_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <android/log.h>

// 获取模块基址
static inline long GetModuleBase(const char *name)
{
    char buff[1024];
    long address = 0;
    FILE *fp = fopen("/proc/self/maps", "r");
    while (fgets(buff, sizeof(buff), fp))
    {
        if (strstr(buff, name))
        {
            sscanf(buff, "%lx-%*lx", &address);
            fclose(fp);
            return address;
        }
    }
    fclose(fp);
    return 0;
}

// 获取模块总大小，并跳过权限为 ---p 的段
static inline long GetModuleTotalSizeWithPermissions(const char *name, const char *log_tag)
{
    char buff[1024];
    long total_size = 0;
    long start = 0, end = 0;
    char perms[5];

    FILE *fp = fopen("/proc/self/maps", "r");
    if (!fp)
    {
        __android_log_print(ANDROID_LOG_ERROR, log_tag, "无法打开 /proc/self/maps 文件");
        return 0;
    }

    while (fgets(buff, sizeof(buff), fp))
    {
        if (strstr(buff, name))
        {
            sscanf(buff, "%lx-%lx %4s", &start, &end, perms);
            if (perms[0] != '-' || perms[1] != '-' || perms[2] != '-' || perms[3] != 'p')
            {
                total_size += (end - start);
            }
            __android_log_print(ANDROID_LOG_INFO, log_tag, "[模块段] %s 段地址: 0x%lx-0x%lx 权限: %s", name, start, end, perms);
        }
    }
    fclose(fp);
    return total_size;
}

// 复制内存时，跳过 ---p 段
static inline void CopyModuleWithoutProtectiveSegments(long old_base, long new_base, long size, const char *module_name, const char *log_tag)
{
    char buff[1024];
    FILE *fp = fopen("/proc/self/maps", "r");
    if (!fp)
    {
        __android_log_print(ANDROID_LOG_ERROR, log_tag, "无法打开 /proc/self/maps 文件");
        return;
    }

    while (fgets(buff, sizeof(buff), fp))
    {
        if (strstr(buff, module_name))
        {
            long start, end;
            char perms[5];
            sscanf(buff, "%lx-%lx %4s", &start, &end, perms);
            __android_log_print(ANDROID_LOG_INFO, log_tag, "检测到内存段: 0x%lx-0x%lx, 权限: %s", start, end, perms);

            if (perms[0] != '-' || perms[1] != '-' || perms[2] != '-' || perms[3] != 'p')
            {
                __android_log_print(ANDROID_LOG_INFO, log_tag, "准备复制内存段: 0x%lx-0x%lx", start, end);
                memcpy((void *)(new_base + (start - old_base)), (void *)start, end - start);
                __android_log_print(ANDROID_LOG_INFO, log_tag, "复制内存段: 0x%lx-0x%lx 成功", start, end);
            }
            else
            {
                __android_log_print(ANDROID_LOG_INFO, log_tag, "跳过内存段: 0x%lx-0x%lx (权限为 ---p)", start, end);
            }
        }
    }
    fclose(fp);
}

#endif // HOOK_UTILS_H 