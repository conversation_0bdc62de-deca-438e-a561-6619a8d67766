import http.client

conn = http.client.HTTPSConnection("api.bilibili.com")

payload = "access_key=442473291433def6a3d11ee8c00d3771CjAeXbixJj3MqmEX0JJnIdFl-1AYsXD3nhwi84FXD768QxKMRkCd3Gh4RMlGm8Sl5BMSVk1TUFVzQ0tyTFBZOFYxZ3F6NHpsMTQ3SDJQem5kSm9FS19JZ0NSY3NiYnZDOTctUnpXVEQ5OWVLVThzS2tIMV9FQTAtbjRqZWF5ZkFOZW1iMktPTW5nIIEC&action=1&appkey=1d8b6e7d45233436&oid=114925439554828&rpid=269099287377&type=1"

headers = {
  'User-Agent': "Mozilla/5.0 BiliDroid/7.76.0 (<EMAIL>) os/android model/23117RK66C mobi_app/android build/7760700 channel/xiaomi_cn_tv.danmaku.bili_20210930 innerVer/7760710 osVer/15 network/2",
  'Content-Type': "application/x-www-form-urlencoded",
  'buvid': "XU9B8919FE8A69861B8105A55CA82EFB7385A",
  'fp_local': "97b38a9670fa0b7517c331c8a02bbaea20250214215043a8cce8c62831d81008",
  'fp_remote': "97b38a9670fa0b7517c331c8a02bbaea20250214215043a8cce8c62831d81008",
  'session_id': "164bc3b7",
  'guestid': "24252376451257",
  'env': "prod",
  'app-key': "android64",
  'x-bili-trace-id': "cbc658285da7da1915c35dacbe6886fe:15c35dacbe6886fe:0:0",
  'x-bili-aurora-eid': "U1wJRVkDAVUP",
  'x-bili-mid': "288387748",
  'x-bili-aurora-zone': "",
  'x-bili-gaia-vtoken': "",
  'x-bili-ticket': "eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MDYxMDAsImlhdCI6MTc1MzY3NzAwMCwiYnV2aWQiOiJYVTlCODkxOUZFOEE2OTg2MUI4MTA1QTU1Q0E4MkVGQjczODVBIn0.jyBBB5jfL5vLWZO-X57l-_Lk9i03B7v7al44sOwRA4I",
  'bili-http-engine': "cronet",
  'content-type': "application/x-www-form-urlencoded; charset=utf-8"
}

conn.request("POST", "/x/v2/reply/hate", payload, headers)

res = conn.getresponse()
data = res.read()

print(data.decode("utf-8"))