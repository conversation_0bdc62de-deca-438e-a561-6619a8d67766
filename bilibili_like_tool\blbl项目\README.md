# 哔哩哔哩批量点赞工具

一个用于哔哩哔哩评论批量点赞和差评点赞的Python工具，支持多账号管理和批量操作。

## 功能特性

- 🚀 支持批量点赞/取消点赞
- 👎 支持批量差评点赞/取消差评
- 👥 多账号管理
- 🎯 **支持账号范围选择**（避免重复点赞）
- ⚙️ 可配置请求间隔
- 📝 详细的操作日志
- 🛡️ 错误处理和异常捕获

## 项目结构

```
bilibili_like_tool/
├── main.py                 # 主程序文件（点赞工具）
├── hate.py                 # 差评点赞工具
├── config_manager.py       # 账号配置管理工具
├── accounts_example.json   # 账号配置文件示例
├── accounts.json          # 实际账号配置文件 (需要自己创建)
├── run.bat                # Windows快速启动脚本
├── requirements.txt        # 依赖说明文件
└── README.md              # 项目说明文档
```

## 安装和使用

### 1. 环境要求

- Python 3.6+
- 无需额外依赖包，使用Python标准库

### 2. 配置账号信息

#### 方式一：使用配置管理工具（推荐）

```bash
python config_manager.py
```

按照提示添加、编辑或删除账号信息。

#### 方式二：手动创建配置文件

复制 `accounts_example.json` 为 `accounts.json`，然后编辑：

```json
{
  "accounts": [
    {
      "name": "账号1",
      "access_key": "你的access_key1",
      "appkey": "你的appkey1"
    },
    {
      "name": "账号2", 
      "access_key": "你的access_key2",
      "appkey": "你的appkey2"
    }
  ]
}
```

### 3. 获取必要参数

使用抓包工具（如Fiddler、Charles等）获取以下参数：

- **access_key**: 账号的访问密钥
- **appkey**: 应用密钥
- **oid**: 视频或评论位置ID
- **rpid**: 评论ID

### 4. 运行程序

#### 正常点赞
```bash
python main.py
```

#### 差评点赞
```bash
python hate.py
```

#### 使用快速启动脚本（Windows）
```bash
run.bat
```

按照提示输入相应参数即可开始批量操作。

## 参数说明

### 配置文件参数

- `name`: 账号名称（用于标识，可自定义）
- `access_key`: 账号的访问密钥
- `appkey`: 应用密钥

### 运行时参数

- `oid`: 视频或评论位置ID
- `rpid`: 评论ID  
- `action`: 操作类型
  - **main.py**: 1=点赞，0=取消点赞
  - **hate.py**: 1=差评点赞，0=取消差评
- `type`: 评论类型
  - `1`: 视频评论
  - `11`: 动态帖子评论
- `delay`: 请求间隔时间（秒，避免请求过于频繁）
- `账号范围`: 指定使用的账号范围（可选）
  - 格式：`开始位置-结束位置`，如 `26-35`
  - 单个账号：直接输入数字，如 `10`
  - 空值：使用全部账号

## 使用示例

### 正常点赞示例

#### 使用全部账号点赞
1. 启动程序：`python main.py`
2. 输入参数：
   ```
   请输入 oid (视频或评论位置ID): 114821018162286
   请输入 rpid (评论ID): 268208256864
   请输入操作类型 (1=点赞, 0=取消点赞) [默认1]: 1
   请输入 type (评论类型) [默认1]: 1
   请输入请求间隔时间(秒) [默认1.0]: 2
   请输入要使用的账号范围 (格式: 26-35，单个数字如 10，或直接回车使用全部): 
   ```
3. 确认执行，开始批量点赞

#### 使用指定范围账号点赞
1. 启动程序：`python main.py`
2. 输入参数：
   ```
   请输入 oid (视频或评论位置ID): 114821018162286
   请输入 rpid (评论ID): 268208256864
   请输入操作类型 (1=点赞, 0=取消点赞) [默认1]: 1
   请输入 type (评论类型) [默认1]: 1
   请输入请求间隔时间(秒) [默认1.0]: 2
   请输入要使用的账号范围 (格式: 26-35，单个数字如 10，或直接回车使用全部): 60-70
   ```
3. 确认执行，使用第60-70个账号进行批量点赞

#### 实际应用场景
**场景1**: 已有60个账号点过赞，新增10个账号
```
账号范围输入: 61-70
结果: 只使用新增的10个账号进行点赞
```

**场景2**: 分批次操作降低风险
```
第一次: 1-30 （使用前30个账号）
第二次: 31-60 （使用中间30个账号） 
第三次: 61-90 （使用后30个账号）
```

**场景3**: 测试单个账号
```
账号范围输入: 1
结果: 只使用第1个账号测试功能
```

### 差评点赞示例
1. 启动程序：`python hate.py`
2. 输入参数：
   ```
   请输入 oid (视频或评论位置ID): 114925439554828
   请输入 rpid (评论ID): 269099287377
   请输入操作类型 (1=差评点赞, 0=取消差评) [默认1]: 1
   请输入 type (评论类型) [默认1]: 1
   请输入请求间隔时间(秒) [默认1.0]: 2
   ```
3. 确认执行，开始批量差评点赞

## 注意事项

⚠️ **重要提醒**

1. **合规使用**: 请遵守哔哩哔哩的用户协议和社区规范
2. **频率控制**: 建议设置适当的请求间隔（1-3秒），避免被限制
3. **账号安全**: 妥善保管access_key等敏感信息
4. **测试先行**: 建议先用少量账号测试功能
5. **风险自负**: 使用本工具产生的任何后果由用户自行承担

## 常见问题

### Q: 如何获取access_key和appkey？
A: 使用抓包工具在手机或网页版哔哩哔哩进行点赞操作时捕获请求参数。

### Q: 为什么点赞失败？
A: 可能的原因：
- access_key或appkey已过期
- oid或rpid参数错误
- 网络连接问题
- 被B站限制频率

### Q: 如何批量取消点赞？
A: 将action参数设置为0即可。

### Q: 正常点赞和差评点赞有什么区别？
A: 
- 正常点赞（main.py）：使用 `/x/v2/reply/action` 接口
- 差评点赞（hate.py）：使用 `/x/v2/reply/hate` 接口，相当于点击“倒竖拇指”

### Q: type 参数的值有什么意义？
A: 
- `1`: 视频评论
- `11`: 动态帖子评论

### Q: 如何使用账号范围选择功能？
A: 在程序运行时会询问账号范围，支持以下格式：
- `26-35`: 使用第26到35个账号（共10个账号）
- `60`: 只使用第60个账号
- 直接回车: 使用全部账号

### Q: 为什么需要账号范围选择功能？
A: 
- **避免重复点赞**: 如果你已经用前60个账号点过赞，可以设置 `61-100` 只用新账号
- **分批操作**: 可以分批次执行，降低被检测风险
- **测试功能**: 可以先用单个账号测试，如输入 `1`

### Q: 账号范围的数字是什么意思？
A: 数字代表账号在配置文件中的位置（从1开始计数）。例如配置文件中的第一个账号是1号，第二个是2号，以此类推。

## 更新日志

### v1.2.0
- 🎯 **新增账号范围选择功能**
- 支持指定账号范围（如 `26-35`）进行批量操作
- 避免重复点赞，提高效率
- 支持分批次操作，降低风险
- 完善错误处理和用户体验

### v1.1.0
- 新增差评点赞功能（hate.py）
- 优化错误码处理，新增-106错误码支持
- 添加Windows快速启动脚本
- 更新文档和示例

### v1.0.0
- 实现基本的批量点赞功能
- 支持多账号管理
- 添加配置管理工具
- 完善错误处理

## 免责声明

本工具仅供学习和研究使用，使用者应当遵守相关法律法规和平台规则。作者不对使用本工具产生的任何后果承担责任。

## 许可证

MIT License
