// 通过 Dobby 对 libanogs.so 中的 sub_1E8A6C 函数做函数级 Hook，
// 在需要时临时拉高 0x2AA 偏移处字节的 bit0，避免长时间后台状态导致封禁
// 中文注释，代码英文

#include <android/log.h>
#include <dlfcn.h>
#include <unistd.h>

#include "hook_utils.h"          // GetModuleBase 实现
#include "Dobby/dobby.h"
#include "semaphores.h"
#include <pthread.h>
#include <sys/mman.h>

#define LOG_TAG "DisableCheckHook"

// sub_1E8A6C 在目标 SO 中的相对偏移，根据实际版本调整
static const long kSubOffset = 0x1E8A6C;

// 不再使用函数级 Hook，直接修改指令立即数 (0x2AA -> 0xA)

// 前向声明
static void install_disable_patch();

// 等待信号并延迟 50 秒后安装 Hook 的线程
static void *wait_and_patch_thread(void *) {
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[线程] 等待 parallel_start_sem...");
    sem_wait(&parallel_start_sem);
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[线程] 收到信号，50 秒后安装 DisableCheck Patch");
    sleep(50);
    install_disable_patch();
    return nullptr;
}

// 模块加载后自动安装 Hook，需等待 cp-cdn 完成信号
__attribute__((constructor(INIT_PRIORITY_FUNC))) static void init_disable_check_hook() {
    pthread_t t;
    pthread_create(&t, nullptr, wait_and_patch_thread, nullptr);
    pthread_detach(t);
}

// 实际安装函数
static bool patch_sub_instruction() {
    long base = GetModuleBase("libanogs.so");
    if (!base) {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "GetModuleBase failed");
        return false;
    }

    // LDRB 位于函数第 3 条指令 (偏移 0xC)
    void *addr = reinterpret_cast<void *>(base + kSubOffset + 0xC);
    uint32_t *ins = reinterpret_cast<uint32_t *>(addr);

    // 改写立即数: bits[21:10] 为 12 位无符号偏移
    uint32_t orig = *ins;
    uint32_t new_val = (orig & ~(0xFFFu << 10)) | (0xAu << 10);

    // 修改内存属性
    size_t page_size = sysconf(_SC_PAGESIZE);
    uintptr_t page_start = (reinterpret_cast<uintptr_t>(addr)) & ~(page_size - 1);
    if (mprotect(reinterpret_cast<void *>(page_start), page_size, PROT_READ | PROT_WRITE | PROT_EXEC) != 0) {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "mprotect failed");
        return false;
    }

    *ins = new_val;
    __builtin___clear_cache(reinterpret_cast<char *>(addr), reinterpret_cast<char *>(addr) + sizeof(uint32_t));

    // 恢复只读 + exec
    mprotect(reinterpret_cast<void *>(page_start), page_size, PROT_READ | PROT_EXEC);

    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "Patched LDRB imm from 0x2AA to 0xA at %p", addr);
    return true;
}

static void install_disable_patch() {
    patch_sub_instruction();
}
