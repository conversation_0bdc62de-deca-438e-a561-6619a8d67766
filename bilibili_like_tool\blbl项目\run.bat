@echo off
chcp 65001 >nul
title 哔哩哔哩批量点赞工具

echo.
echo ==========================================
echo           哔哩哔哩批量点赞工具
echo ==========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请先安装Python 3.6+
    pause
    exit /b 1
)

:: 检查配置文件是否存在
if not exist "accounts.json" (
    echo 未找到账号配置文件，正在启动配置管理工具...
    echo.
    python config_manager.py
    echo.
    echo 配置完成后请重新运行此脚本
    pause
    exit /b 0
)

:: 运行主程序
python main.py

echo.
pause
