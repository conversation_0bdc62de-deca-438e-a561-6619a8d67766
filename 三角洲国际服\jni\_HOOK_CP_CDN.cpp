#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <android/log.h>
#include "Dobby/dobby.h"
#include "hook_utils.h"
#include "semaphores.h"

#define LOG_TAG "MyHook_CP_CDN"

// 原始函数指针
static int (*orig_cp_cdn_func)(__int64);
static bool cp_cdn_hook_installed = false;

// 替代函数 - 直接返回0，表示不执行原始逻辑
static int fake_cp_cdn_func(__int64 a1) {
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[拦截] 捕获到cp-cdn调用，参数: %lld", a1);
    return 0;  // 直接返回0，表示不执行原始逻辑
}

int ReverseCpCdn() {
    // 等待 memcpy 初始化完成
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[等待] 等待 Memcpy Hook 初始化完成...");
    sem_wait(&memcpy_done_sem);
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[初始化] ============= 开始初始化 cp-cdn Hook =============");
    
    // 循环获取libanogs.so的基地址
    void* base_addr = nullptr;
    while (!base_addr) {
        long module_base = GetModuleBase("libanogs.so");
        if (!module_base) {
            __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, 
                "[等待] libanogs.so还未加载，1秒后重试...");
            sleep(1);
            continue;
        }
        base_addr = (void*)module_base;
    }
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[成功] 获取到libanogs.so基地址: %p", base_addr);
    
    // 计算函数入口的实际地址 (偏移量0x395F28)
    void *target_addr = (void *)((uintptr_t)base_addr + 0x395F28);
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[信息] cp-cdn地址: %p (base: %p + offset: 0x395F28)", target_addr, base_addr);
    // 安装Hook函数
    if (DobbyHook(target_addr, (void *)fake_cp_cdn_func, (void **)&orig_cp_cdn_func) == 0) {
        cp_cdn_hook_installed = true;
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
            "[成功] cp-cdn Hook 安装成功");
    } else {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, 
            "[错误] cp-cdn Hook 安装失败");
        return -1;
    }
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[初始化] ============= cp-cdn Hook 初始化完成 =============");
    
    // 发出4个信号，通知其他模块可以开始执行 (FUNC / TersafeMincore / SystemMincore / DisableCheck)
    for(int i = 0; i < 4; i++) {
        sem_post(&parallel_start_sem);
    }
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[完成] cp-cdn Hook 初始化完成，发出信号给其他4个模块...");
    return 0;
}

void *init_for_cp_cdn(void *) {
    ReverseCpCdn();
    return 0;
}

// 使用定义的宏
void __attribute__ ((constructor(INIT_PRIORITY_CP_CDN))) init_cp_cdn() {
    pthread_t t;
    pthread_create(&t, 0, init_for_cp_cdn, 0);
    pthread_detach(t);
}