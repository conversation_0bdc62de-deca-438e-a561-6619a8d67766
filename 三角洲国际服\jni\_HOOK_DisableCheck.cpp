// 通过 Dobby 对 libanogs.so 中的 sub_1E8A6C 函数做函数级 Hook，只修改返回值
// 中文注释，代码英文

#include <android/log.h>
#include <dlfcn.h>
#include <unistd.h>

#include "hook_utils.h"          // GetModuleBase 实现
#include "Dobby/dobby.h"
#include "semaphores.h"
#include <pthread.h>

#define LOG_TAG "DisableCheckHook"

// sub_1E8A6C 在目标 SO 中的相对偏移，根据实际版本调整
static const long kSubOffset = 0x1E8A6C;

// 原始函数指针
static uint32_t (*orig_sub)(void *ptr) = nullptr;

// Hook 函数：只返回 1，不写 flag
static uint32_t my_sub(void *ptr) {
    (void)ptr;
    return 1;
}

// 前向声明
static void install_disable_hook();

// 等待信号并延迟 50 秒后安装 Hook 的线程
static void *wait_and_hook_thread(void *) {
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[线程] 等待 parallel_start_sem...");
    sem_wait(&parallel_start_sem);
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[线程] 收到信号，50 秒后安装 DisableCheck Hook");
    sleep(50);
    install_disable_hook();
    return nullptr;
}

// 模块加载后自动安装 Hook，需等待 cp-cdn 完成信号
__attribute__((constructor(INIT_PRIORITY_FUNC))) static void init_disable_check_hook() {
    pthread_t t;
    pthread_create(&t, nullptr, wait_and_hook_thread, nullptr);
    pthread_detach(t);
}

// 安装 Hook
static void install_disable_hook() {
    long base = GetModuleBase("libanogs.so");
    if (!base) {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "GetModuleBase failed");
        return;
    }
    void *target = reinterpret_cast<void *>(base + kSubOffset);
    if (DobbyHook(target, reinterpret_cast<void *>(my_sub), reinterpret_cast<void **>(&orig_sub)) == 0) {
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "Hook installed at %p (base 0x%lx)", target, base);
    } else {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "Failed to install hook at %p", target);
    }
}
