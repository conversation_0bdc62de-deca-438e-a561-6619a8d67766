#ifndef SEMAPHORES_H
#define SEMAPHORES_H

#include <semaphore.h>

// 声明信号量
extern sem_t memcpy_done_sem;      // memcpy -> cp-cdn
extern sem_t parallel_start_sem;    // cp-cdn -> 其他4个模块

// 定义初始化优先级
#define INIT_PRIORITY_MEMCPY          101  // 第一个执行 (_HOOK_MEMCPY.cpp)
#define INIT_PRIORITY_CP_CDN          102  // 第二个执行 (_HOOK_CP_CDN.cpp)
#define INIT_PRIORITY_FUNC            103  // 并行执行 (_HOOK_FUNC.cpp)
#define INIT_PRIORITY_TERSAFE_MINCORE 103  // 并行执行 (_HOOK_TersafeMincore.cpp)
#define INIT_PRIORITY_SYSTEM_MINCORE  103  // 并行执行 (_HOOK_SystemMincore.cpp)

// 初始化所有信号量
void init_semaphores();

#endif // SEMAPHORES_H 