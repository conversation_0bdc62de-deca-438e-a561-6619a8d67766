#include <dirent.h>
#include <fcntl.h>
#include <sys/ptrace.h>
#include <sys/wait.h>
#include <sys/user.h>
#include <sys/syscall.h>
#include <sys/uio.h>
#include <unistd.h>
#include <cstring>
#include <cstdio>
#include <cstdlib>
#include <string>
#include <iostream>
#include <signal.h>
#include <vector>
#include <thread>
#include <atomic>
#include <algorithm>
#include <android/log.h>

// 兼容性处理：有些环境没有定义NT_PRSTATUS
#ifndef NT_PRSTATUS
#define NT_PRSTATUS 1
#endif

#define TARGET_PKG "com.tencent.tmgp.dfm"
#define TARGET_ACTIVITY "com.epicgames.ue4.GameActivity"

#define LOGI(fmt, ...) __android_log_print(ANDROID_LOG_INFO, "myhook", fmt, ##__VA_ARGS__)

volatile bool stop_flag = false;
void sigint_handler(int) {
    stop_flag = true;
}

std::atomic<pid_t> found_pid(-1);

void check_pid_range(const std::vector<int>& pids, const char* packageName, size_t start, size_t end) {
    for (size_t i = start; i < end && found_pid == -1; ++i) {
        int pid = pids[i];
        char file_name[30];
        char temp_name[50];
        snprintf(file_name, 30, "/proc/%d/cmdline", pid);
        FILE *fp = fopen(file_name, "r");
        if (fp != nullptr) {
            fgets(temp_name, 50, fp);
            fclose(fp);
            if (strcmp(packageName, temp_name) == 0) {
                found_pid = pid;
                return;
            }
        }
    }
}

pid_t find_process_id_multithread(const char *packageName, int thread_count = 4) {
    std::vector<int> pids;
    DIR* dir = opendir("/proc");
    if (packageName == nullptr || dir == nullptr) {
        return -1;
    }
    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        int pid = atoi(entry->d_name);
        if (pid > 0) pids.push_back(pid);
    }
    closedir(dir);

    found_pid = -1;
    std::vector<std::thread> threads;
    size_t per_thread = (pids.size() + thread_count - 1) / thread_count;
    for (int t = 0; t < thread_count; ++t) {
        size_t start = t * per_thread;
        size_t end = std::min(start + per_thread, pids.size());
        threads.emplace_back(check_pid_range, std::ref(pids), packageName, start, end);
    }
    for (auto& th : threads) th.join();
    return found_pid;
}

int main() {
    signal(SIGINT, sigint_handler); // 捕获Ctrl+C

    // 先启动目标Activity
    std::string am_cmd = "am start -n " TARGET_PKG "/" TARGET_ACTIVITY;
    LOGI("启动目标Activity: %s", am_cmd.c_str());
    system(am_cmd.c_str());

    pid_t target_pid = -1;
    int ue4_detect_count = 0; // 记录检测到libUE4.so的次数

    // 1. 多线程查找进程
    while (true) {
        target_pid = find_process_id_multithread(TARGET_PKG, 4); // 4线程
        if (target_pid > 0) {
            LOGI("检测到目标进程: %s, PID=%d", TARGET_PKG, target_pid);
            break;
        }
        // 不再sleep，立即下一轮查找
    }

    // 2. ptrace attach
    if (ptrace(PTRACE_ATTACH, target_pid, NULL, NULL) == -1) {
        perror("ptrace attach 失败");
        return 1;
    }
    LOGI("已附加到进程 %d", target_pid);

    int status = 0;
    waitpid(target_pid, &status, 0);

    // 3. 设置ptrace选项
    ptrace(PTRACE_SETOPTIONS, target_pid, 0, PTRACE_O_TRACESYSGOOD);

    // 4. 系统调用拦截循环
    bool in_syscall = false;
    while (!stop_flag) {
        ptrace(PTRACE_SYSCALL, target_pid, 0, 0);
        int status = 0;
        waitpid(target_pid, &status, 0);

        if (WIFEXITED(status) || WIFSIGNALED(status)) break;

        int sig = 0;
        if (WIFSTOPPED(status)) {
            sig = WSTOPSIG(status);
            if (sig != (SIGTRAP | 0x80)) {
                // 不是系统调用陷阱，原样传递信号
                ptrace(PTRACE_SYSCALL, target_pid, 0, sig);
                continue;
            }
        }

        struct user_pt_regs regs;
        struct iovec iov = { &regs, sizeof(regs) };
        ptrace(PTRACE_GETREGSET, target_pid, (void*)NT_PRSTATUS, &iov);

        if (!in_syscall) {
            // 进入系统调用
            if (regs.regs[8] == __NR_openat) {
                unsigned long pathname_addr = regs.regs[1];
                char path[256] = {0};
                for (int i = 0; i < 255; i += sizeof(long)) {
                    long val = ptrace(PTRACE_PEEKDATA, target_pid, pathname_addr + i, NULL);
                    if (val == -1 && errno) break;
                    memcpy(path + i, &val, sizeof(long));
                    if (memchr(&val, 0, sizeof(long))) break;
                }
                // 判断是否以.so结尾
                size_t len = strlen(path);
                if (len > 3 && strcmp(path + len - 3, ".so") == 0) {
                    LOGI("检测到openat打开.so文件: %s", path);
                }
            }
            in_syscall = true;
        } else {
            // 退出系统调用
            in_syscall = false;
        }
    }

    ptrace(PTRACE_DETACH, target_pid, NULL, NULL);
    LOGI("监控结束");
    return 0;
}