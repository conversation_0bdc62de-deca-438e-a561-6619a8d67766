#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <android/log.h>
#include "Dobby/dobby.h"
#include "hook_utils.h"
#include "semaphores.h"

#define LOG_TAG "MyHook_FUNC"

// 原始函数指针
static int (*orig_func)(__int64);
static bool func_hook_installed = false;

// 替代函数 - 直接返回 1 ，表示执行原始逻辑
static int fake_func(__int64 a1) {
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[拦截] 捕获到FUNC调用，参数: %lld", a1);
    return 1;  // 直接返回 1 ，表示执行原始逻辑
}

int ReverseFunc() {
    // 等待 cp_cdn 初始化完成
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[等待] 等待 CP_CDN Hook 初始化完成...");
    sem_wait(&parallel_start_sem);
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[初始化] ============= 开始初始化 FUNC Hook =============");
    
    // 循环获取libtersafe.so的基地址
    void* base_addr = nullptr;
    while (!base_addr) {
        long module_base = GetModuleBase("libtersafe.so");
        if (!module_base) {
            __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, 
                "[等待] libtersafe.so还未加载，1秒后重试...");
            sleep(1);
            continue;
        }
        base_addr = (void*)module_base;
    }
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[成功] 获取到libtersafe.so基地址: %p", base_addr);
    
    // 计算函数入口的实际地址 (偏移量283170)
    void *target_addr = (void *)((uintptr_t)base_addr + 0x283170);
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[信息] FUNC地址: %p (base: %p + offset: 0x283170)", target_addr, base_addr);
    // 安装Hook函数
    if (DobbyHook(target_addr, (void *)fake_func, (void **)&orig_func) == 0) {
        func_hook_installed = true;
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
            "[成功] FUNC Hook 安装成功");
    } else {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, 
            "[错误] FUNC Hook 安装失败");
        return -1;
    }
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[初始化] ============= FUNC Hook 初始化完成 =============");
    return 0;
}

void *init_for_func(void *) {
    ReverseFunc();
    return 0;
}

// 使用定义的宏
void __attribute__ ((constructor(INIT_PRIORITY_FUNC))) init_func() {
    pthread_t t;
    pthread_create(&t, 0, init_for_func, 0);
    pthread_detach(t);
}