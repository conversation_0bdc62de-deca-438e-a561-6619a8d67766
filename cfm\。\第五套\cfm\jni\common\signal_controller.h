#ifndef SIGNAL_CONTROLLER_H
#define SIGNAL_CONTROLLER_H

#include <pthread.h>
#include <android/log.h>

#define LOG_TAG_SIGNAL "SignalController"

// 全局互斥锁和条件变量
extern pthread_mutex_t g_hook_mutex;
extern pthread_cond_t g_hook_cond;
extern volatile int g_unified_completed;

// 初始化信号控制器
void init_signal_controller();

// 发送信号表示_HOOK_UNIFIED已完成
void signal_unified_completed();

// 等待_HOOK_UNIFIED完成的信号
void wait_for_unified_completion();

#endif // SIGNAL_CONTROLLER_H 