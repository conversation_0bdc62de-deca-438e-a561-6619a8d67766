#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <android/log.h>
#include "Dobby/dobby.h"
#include "hook_utils.h"
#include "semaphores.h"

#define LOG_TAG "MyHook_MEMCPY"

// anogs/tersafe 相关变量
static long new_anogs, anogs_base, anogs_size = 0;
// UE4/Unity 相关变量
static long new_ue4, ue4_base, ue4_size = 0;
static bool hook_installed = false;

// 前向声明
static void *ue4_setup_thread(void *);

// 统一的 hook 函数
void *(*original_memcpy)(void *dest, long src, int n);
void *unified_memcpy_hook(void *dest, long src, int n)
{
    if (!hook_installed) return original_memcpy(dest, src, n);
    
    // 检查是否是 anogs/tersafe 的地址范围
    if (src >= anogs_base && src < (anogs_base + anogs_size))
    {
        long new_srcPtr = new_anogs + (src - anogs_base);
        src = new_srcPtr;
        //__android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, 
        //    "[重定向] anogs/tersafe 内存访问: 0x%lx -> 0x%lx", (long)src, new_srcPtr);
    }
    // 检查是否是 UE4/Unity 的地址范围
    else if (src >= ue4_base && src < (ue4_base + ue4_size))
    {
        long new_srcPtr = new_ue4 + (src - ue4_base);
        src = new_srcPtr;
        //__android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, 
        //    "[重定向] UE4/Unity 内存访问: 0x%lx -> 0x%lx", (long)src, new_srcPtr);
    }
    
    return original_memcpy(dest, src, n);
}

// 处理 anogs/tersafe 模块
bool setup_anogs()
{
    const char *candidates[] = {"libtersafe.so", "libanogs.so"};
    const char *found_module = nullptr;

    // 获取模块基址
    while (!anogs_base) {
        for (int i = 0; i < 2 && !anogs_base; ++i) {
            anogs_base = GetModuleBase(candidates[i]);
            if (anogs_base) {
                found_module = candidates[i];
            }
        }
        if (!anogs_base) {
            __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, "[等待] anogs/tersafe 模块未加载，1s 后重试...");
            sleep(1);
        }
    }

    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[模块检测] 找到模块 %s 基地址: 0x%lx", found_module, anogs_base);

    // 获取模块大小
    anogs_size = GetModuleTotalSizeWithPermissions(found_module, LOG_TAG);
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[模块大小] %s 模块总大小: 0x%lx", found_module, anogs_size);

    // 分配内存
    new_anogs = (long)malloc(anogs_size) & (long)0xffffffffff;
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[内存分配] new_anogs 地址: 0x%lx", new_anogs);

    // 复制内存
    CopyModuleWithoutProtectiveSegments(anogs_base, new_anogs, anogs_size, found_module, LOG_TAG);
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[内存复制] 已将 %s 模块内容复制到新的内存区域", found_module);

    return true;
}

// 处理 UE4/Unity 模块
bool setup_ue4()
{
    const char *candidates[] = {"libUE4.so", "libunity.so"};
    const char *found_module = nullptr;

    // 循环检测两个可能的模块名
    while (!ue4_base) {
        for (int i = 0; i < 2 && !ue4_base; ++i) {
            ue4_base = GetModuleBase(candidates[i]);
            if (ue4_base) {
                found_module = candidates[i];
            }
        }
        if (!ue4_base) {
            __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, "[等待] UE4/Unity 模块未加载，1s 后重试...");
            sleep(1);
        }
    }

    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[模块检测] 找到模块 %s 基地址: 0x%lx", found_module, ue4_base);

    // 获取模块大小
    ue4_size = GetModuleTotalSizeWithPermissions(found_module, LOG_TAG);
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[模块大小] %s 模块总大小: 0x%lx", found_module, ue4_size);

    // 分配内存
    new_ue4 = (long)malloc(ue4_size) & (long)0xffffffffff;
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[内存分配] new_ue4 地址: 0x%lx", new_ue4);

    // 复制内存
    CopyModuleWithoutProtectiveSegments(ue4_base, new_ue4, ue4_size, found_module, LOG_TAG);
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[内存复制] 已将 %s 模块内容复制到新的内存区域", found_module);

    return true;
}

// 主要的初始化函数
int ReverseCheatingMemcpy()
{
    // 先设置 anogs/tersafe
    if (!setup_anogs()) {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, 
            "[错误] anogs/tersafe 模块设置失败");
        return -1;
    }
    
    // 启动后台线程等待 UE4/Unity 加载并复制
    pthread_t t_ue4;
    pthread_create(&t_ue4, nullptr, ue4_setup_thread, nullptr);
    pthread_detach(t_ue4);
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[后台] 开启线程等待 UE4/Unity 模块加载 ...");
    
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, 
        "[提示] Anogs/Tersafe 复制完成，等待 UE4/Unity 复制结束后再安装 memcpy hook 并通知 cp-cdn...");
    return 0;
}

// 后台线程函数：等待 UE4/Unity 并复制
static void *ue4_setup_thread(void *) {
    bool ue4_ok = setup_ue4();

    // 安装统一 memcpy hook (此时两个模块均已复制)
    if (DobbyHook((void *)memcpy, (void *)unified_memcpy_hook, (void **)&original_memcpy) == 0) {
        hook_installed = true;
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[成功] 成功安装统一的 memcpy hook");
    } else {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "[错误] 安装统一的 memcpy hook 失败");
    }

    if (ue4_ok) {
        __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "[完成] UE4/Unity 复制完成，通知 cp-cdn 开始...");
    } else {
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "[错误] UE4/Unity 复制失败，但仍通知 cp-cdn 避免死锁");
    }
    sem_post(&memcpy_done_sem);
    return nullptr;
}

void *init_thread_memcpy(void *)
{
    ReverseCheatingMemcpy();
    return 0;
}

void __attribute__ ((constructor(INIT_PRIORITY_MEMCPY))) init_memcpy()
{
    // 使用封装函数初始化所有信号量
    init_semaphores();
    
    pthread_t t;
    pthread_create(&t, 0, init_thread_memcpy, 0);
    pthread_detach(t);
} 