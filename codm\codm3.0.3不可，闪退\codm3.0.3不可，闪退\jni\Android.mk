LOCAL_PATH := $(call my-dir)
MAIN_LOCAL_PATH := $(call my-dir)

# 预编译静态库
include $(CLEAR_VARS)
LOCAL_MODULE := libdobby
LOCAL_SRC_FILES := Dobby/libdobby.a
include $(PREBUILT_STATIC_LIBRARY)

# 主模块
include $(CLEAR_VARS)
LOCAL_MODULE    := UE5
LOCAL_CPPFLAGS := -g0 -O2 -w -fvisibility=hidden -fno-stack-protector -fms-extensions -s -DDBG=0 -DNDEBUG=1 -std=c++17
LOCAL_LDLIBS += -lc -lz -lm -llog -landroid -lEGL -lGLESv2 -lGLESv3 -lGLESv1_CM

# 源文件：库加载器和SO路径查找模块
LOCAL_SRC_FILES := library_loader.cpp so_path_finder.cpp
LOCAL_STATIC_LIBRARIES := libdobby

# 允许未定义符号，兼容动态加载SO（使用dlsym）
LOCAL_LDFLAGS += -Wl,--allow-shlib-undefined

include $(BUILD_SHARED_LIBRARY)