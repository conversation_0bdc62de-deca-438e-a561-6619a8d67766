写一个python程序项目

就是哔哩哔哩一键点赞，发送请求
我已经自己分析到大致信息了，你只需要帮忙做成脚本就行


比如这个
import http.client

conn = http.client.HTTPSConnection("api.bilibili.com")

payload = "access_key=4bf5ea0089d5a453332bcb2dc6b2c271CjBriPJP5Gy_XJrpiZ-3qgXwSx5bqqIIApZZ1nZymRqfKEDnxsVvVGMXiYkQNc4vmLUSVmI0Yl9IUVNZRHlRbGJtUDdNblpiRHQxNUhkbFJHVHkxY2VIWXU0c2dHTE5uODN3N2FDQXIyeVlVNzN5Qkx6UmNwb3Zjc1dNcHhKa25TV3d2Mll2Z093IIEC&action=0&appkey=bb3101000e232e27&oid=114821018162286&rpid=268208256864&type=1"

headers = {
  'Reqable-Id': "reqable-id-41b514a2-ae04-4a28-b16a-2189151a2b1c",
  'User-Agent': "Mozilla/5.0 BiliDroid/7.76.0 (<EMAIL>) os/android model/23117RK66C mobi_app/android build/7760700 channel/xiaomi_cn_tv.danmaku.bili_20210930 innerVer/7760710 osVer/15 network/2",
  'Content-Type': "application/x-www-form-urlencoded",
  'content-type': "application/x-www-form-urlencoded; charset=utf-8",
  'buvid': "XU9B8919FE8A69861B8105A55CA82EFB7385A",
  'fp_local': "97b38a9670fa0b7517c331c8a02bbaea20250214215043a8cce8c62831d81008",
  'fp_remote': "97b38a9670fa0b7517c331c8a02bbaea20250214215043a8cce8c62831d81008",
  'session_id': "2169607a",
  'guestid': "24252376451257",
  'env': "prod",
  'app-key': "android64",
  'x-bili-trace-id': "02d89427529663d677223aede36886e5:77223aede36886e5:0:0",
  'x-bili-aurora-eid': "U1wJRVkDAVUP",
  'x-bili-mid': "288387748",
  'x-bili-aurora-zone': "",
  'x-bili-gaia-vtoken': "",
  'x-bili-ticket': "eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM2OTk5MzIsImlhdCI6MTc1MzY3MDgzMiwiYnV2aWQiOiJYVTlCODkxOUZFOEE2OTg2MUI4MTA1QTU1Q0E4MkVGQjczODVBIn0.5EfZJC7WbKnibg0zDMDOZFmPlwv5cBQK_1CUtVf6bG4",
  'bili-http-engine': "cronet"
}

conn.request("POST", "/x/v2/reply/action", payload, headers)

res = conn.getresponse()
data = res.read()

print(data.decode("utf-8"))

headers = {
里的东西并不重要，就是单纯请求用的随便去点个点赞抓出来都能通用


重要的是这个payload =

access_key，点赞账号的信息
action这个0是取消点赞，1是点赞
appkey，点赞账号的信息
oid，视频或点赞评论的位置
rpid，视频或点赞评论的位置
type这个不清楚保持数值为1就行了


也就是说比如说我们想批量点赞
可以搞个东西储存多个账号的
access_key和appkey
用户要手动填写的就只有oid，rpid，type